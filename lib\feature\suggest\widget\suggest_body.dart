import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/my_text.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/feature/details/details_screen.dart';
import 'package:movie_proj/feature/home/<USER>/movie_model.dart';
import 'package:movie_proj/feature/suggest/manage/top_rated_cubit.dart';
import 'package:movie_proj/feature/suggest/manage/suggest_search_cubit.dart';

class SuggestBody extends StatefulWidget {
  const SuggestBody({super.key});

  @override
  State<SuggestBody> createState() => _SuggestBodyState();
}

class _SuggestBodyState extends State<SuggestBody> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounceTimer;
  final Set<MovieModel> _selectedMovies = {};

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<TopRatedCubit>().refreshTopRated();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              vSpace(30),
              Center(
                child: Text(
                  MyText.forYourTaste,
                  style: MyStyles.title24White700.copyWith(fontSize: 24),
                ),
              ),
              vSpace(16),
              Center(
                child: Text(
                  MyText.choseFiveMovies,
                  style: MyStyles.title24White400.copyWith(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ),
              vSpace(30),
              _buildSearchSection(),
              vSpace(20),
              _buildSelectedMoviesSection(),
              vSpace(30),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search Movies',
          style: MyStyles.title24White700.copyWith(fontSize: 18),
        ),
        vSpace(12),
        Container(
          decoration: BoxDecoration(
            color: MyColors.secondaryColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[700]!, width: 1),
          ),
          child: TextField(
            controller: _searchController,
            style: MyStyles.title24White400.copyWith(fontSize: 14),
            decoration: InputDecoration(
              hintText: 'Search for movies...',
              hintStyle: MyStyles.title24White400.copyWith(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              prefixIcon: const Icon(
                Icons.search,
                color: Colors.grey,
                size: 20,
              ),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(
                        Icons.clear,
                        color: Colors.grey,
                        size: 20,
                      ),
                      onPressed: () {
                        _searchController.clear();
                        context.read<SuggestSearchCubit>().clearSearch();
                        setState(() {});
                      },
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              setState(() {});
              if (value.trim().isNotEmpty) {
                _debounceTimer?.cancel();
                _debounceTimer = Timer(const Duration(milliseconds: 800), () {
                  if (mounted) {
                    context.read<SuggestSearchCubit>().searchMovies(value);
                  }
                });
              } else {
                context.read<SuggestSearchCubit>().clearSearch();
              }
            },
          ),
        ),
        vSpace(16),
        _buildSearchResults(),
      ],
    );
  }

  Widget _buildSearchResults() {
    return BlocBuilder<SuggestSearchCubit, SuggestSearchState>(
      builder: (context, state) {
        if (state is SuggestSearchInitial) {
          return const SizedBox.shrink();
        }

        if (state is SuggestSearchLoading) {
          return Container(
            height: 100,
            alignment: Alignment.center,
            child: const CircularProgressIndicator(
              color: Colors.white54,
              strokeWidth: 2,
            ),
          );
        }

        if (state is SuggestSearchEmpty) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: Text(
                'No movies found for "${state.query}"',
                style: MyStyles.title24White400.copyWith(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ),
          );
        }

        if (state is SuggestSearchError) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: Text(
                'Error: ${state.message}',
                style: MyStyles.title24White400.copyWith(
                  fontSize: 14,
                  color: Colors.red[300],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          );
        }

        if (state is SuggestSearchLoaded) {
          return SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: state.movies.length,
              itemBuilder: (context, index) {
                final movie = state.movies[index];
                final isSelected = _selectedMovies.contains(movie);
                final canSelect = _selectedMovies.length < 5 || isSelected;

                return GestureDetector(
                  onTap: canSelect
                      ? () {
                          setState(() {
                            if (isSelected) {
                              _selectedMovies.remove(movie);
                            } else {
                              _selectedMovies.add(movie);
                            }
                          });
                        }
                      : null,
                  child: Container(
                    width: 120,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected
                            ? Colors.blue
                            : canSelect
                                ? Colors.transparent
                                : Colors.grey[600]!,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: ClipRRect(
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(8),
                            ),
                            child: (movie.posterPath?.isNotEmpty ?? false)
                                ? Image.network(
                                    'https://image.tmdb.org/t/p/w300${movie.posterPath}',
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    errorBuilder: (context, error, stackTrace) {
                                      return _buildPlaceholderImage();
                                    },
                                  )
                                : _buildPlaceholderImage(),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                movie.title ?? 'Unknown Title',
                                style: MyStyles.title24White400.copyWith(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.star,
                                    color: Colors.amber,
                                    size: 12,
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    (movie.voteAverage ?? 0.0)
                                        .toStringAsFixed(1),
                                    style: MyStyles.title24White400.copyWith(
                                      fontSize: 10,
                                      color: Colors.amber,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            decoration: const BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.vertical(
                                bottom: Radius.circular(8),
                              ),
                            ),
                            child: const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        if (!canSelect && !isSelected)
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.grey[600],
                              borderRadius: const BorderRadius.vertical(
                                bottom: Radius.circular(8),
                              ),
                            ),
                            child: Text(
                              'Max 5',
                              style: MyStyles.title24White400.copyWith(
                                fontSize: 10,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildSelectedMoviesSection() {
    if (_selectedMovies.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Selected Movies (${_selectedMovies.length}/5)',
              style: MyStyles.title24White700.copyWith(fontSize: 18),
            ),
            if (_selectedMovies.isNotEmpty)
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedMovies.clear();
                  });
                },
                child: Text(
                  'Clear All',
                  style: MyStyles.title24White400.copyWith(
                    fontSize: 14,
                    color: Colors.red[300],
                  ),
                ),
              ),
          ],
        ),
        vSpace(12),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _selectedMovies.length,
            itemBuilder: (context, index) {
              final movie = _selectedMovies.elementAt(index);
              return GestureDetector(
                onTap: () => _navigateToMovieDetails(context, movie),
                child: Container(
                  width: 110,
                  margin: const EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue, width: 2),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(8),
                              ),
                              child: (movie.posterPath?.isNotEmpty ?? false)
                                  ? Image.network(
                                      'https://image.tmdb.org/t/p/w300${movie.posterPath}',
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return _buildPlaceholderImage();
                                      },
                                    )
                                  : _buildPlaceholderImage(),
                            ),
                            Positioned(
                              top: 4,
                              right: 4,
                              child: GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _selectedMovies.remove(movie);
                                  });
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: const BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(6),
                        child: Text(
                          movie.title ?? 'Unknown Title',
                          style: MyStyles.title24White400.copyWith(
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        vSpace(16),
        if (_selectedMovies.isNotEmpty)
          Center(
            child: ElevatedButton(
              onPressed: () {
                _showRecommendations();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Get Recommendations',
                style: MyStyles.title24White700.copyWith(fontSize: 16),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[800],
      child: const Icon(
        Icons.movie,
        color: Colors.grey,
        size: 40,
      ),
    );
  }

  void _navigateToMovieDetails(BuildContext context, MovieModel movie) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DetailsScreen(movie: movie),
      ),
    );
  }

  void _showRecommendations() {
    // Show a dialog or navigate to recommendations based on selected movies
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: MyColors.secondaryColor,
        title: Text(
          'Recommendations',
          style: MyStyles.title24White700.copyWith(fontSize: 18),
        ),
        content: Text(
          'Based on your ${_selectedMovies.length} selected movies, we would show personalized recommendations here.',
          style: MyStyles.title24White400.copyWith(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'OK',
              style: MyStyles.title24White400.copyWith(
                fontSize: 14,
                color: Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // int _getCrossAxisCount(double width) {
  //   if (width <= 400) return 2;
  //   if (width <= 700) return 3;
  //   return 4;
  // }

  // double _getSpacing(double width) {
  //   if (width <= 400) return 8;
  //   if (width <= 700) return 12;
  //   return 16;
  // }

  // Widget _buildTopRatedCard(TopRatedItem item, double containerWidth) {
  //   final isSmallScreen = containerWidth <= 400;
  //   final isMediumScreen = containerWidth <= 700;

  //   return Container(
  //     decoration: BoxDecoration(
  //       color: MyColors.secondaryColor,
  //       borderRadius: BorderRadius.circular(10),
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Expanded(
  //           flex: 3,
  //           child: Container(
  //             width: double.infinity,
  //             decoration: BoxDecoration(
  //               borderRadius:
  //                   const BorderRadius.vertical(top: Radius.circular(10)),
  //             ),
  //             child: ClipRRect(
  //               borderRadius:
  //                   const BorderRadius.vertical(top: Radius.circular(10)),
  //               child: item.posterPath.isNotEmpty
  //                   ? Image.network(
  //                       'https://image.tmdb.org/t/p/w500${item.posterPath}',
  //                       fit: BoxFit.cover,
  //                       errorBuilder: (context, error, stackTrace) {
  //                         return _buildPlaceholderImage();
  //                       },
  //                       loadingBuilder: (context, child, loadingProgress) {
  //                         if (loadingProgress == null) return child;
  //                         return _buildLoadingImage();
  //                       },
  //                     )
  //                   : _buildPlaceholderImage(),
  //             ),
  //           ),
  //         ),
  //         Expanded(
  //           flex: 1,
  //           child: Padding(
  //             padding: EdgeInsets.all(isSmallScreen ? 8 : 10),
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Text(
  //                   item.title,
  //                   style: MyStyles.title24White400.copyWith(
  //                     fontSize: isSmallScreen ? 10 : (isMediumScreen ? 11 : 12),
  //                     fontWeight: FontWeight.bold,
  //                   ),
  //                   maxLines: 1,
  //                   overflow: TextOverflow.ellipsis,
  //                 ),
  //                 const SizedBox(height: 2),
  //                 Row(
  //                   children: [
  //                     Icon(
  //                       Icons.star,
  //                       color: Colors.amber,
  //                       size: isSmallScreen ? 12 : 14,
  //                     ),
  //                     const SizedBox(width: 2),
  //                     Text(
  //                       item.rating.toStringAsFixed(1),
  //                       style: MyStyles.title24White400.copyWith(
  //                         fontSize: isSmallScreen ? 8 : 10,
  //                         color: Colors.amber,
  //                       ),
  //                     ),
  //                     const SizedBox(width: 8),
  //                     Container(
  //                       padding: const EdgeInsets.symmetric(
  //                           horizontal: 4, vertical: 1),
  //                       decoration: BoxDecoration(
  //                         color: item.isMovie ? Colors.blue : Colors.green,
  //                         borderRadius: BorderRadius.circular(4),
  //                       ),
  //                       child: Text(
  //                         item.isMovie ? 'Movie' : 'TV',
  //                         style: MyStyles.title24White400.copyWith(
  //                           fontSize: isSmallScreen ? 6 : 8,
  //                           color: Colors.white,
  //                         ),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // Widget _buildLoadingCard(double containerWidth) {
  //   final isSmallScreen = containerWidth <= 400;

  //   return Container(
  //     decoration: BoxDecoration(
  //       color: MyColors.secondaryColor,
  //       borderRadius: BorderRadius.circular(10),
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Expanded(
  //           flex: 3,
  //           child: Container(
  //             width: double.infinity,
  //             decoration: BoxDecoration(
  //               borderRadius:
  //                   const BorderRadius.vertical(top: Radius.circular(10)),
  //               color: Colors.grey[800],
  //             ),
  //             child: const Center(
  //               child: CircularProgressIndicator(
  //                 color: Colors.white54,
  //                 strokeWidth: 2,
  //               ),
  //             ),
  //           ),
  //         ),
  //         Expanded(
  //           flex: 1,
  //           child: Padding(
  //             padding: EdgeInsets.all(isSmallScreen ? 8 : 10),
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Container(
  //                   height: 12,
  //                   width: double.infinity,
  //                   decoration: BoxDecoration(
  //                     color: Colors.grey[700],
  //                     borderRadius: BorderRadius.circular(4),
  //                   ),
  //                 ),
  //                 const SizedBox(height: 4),
  //                 Container(
  //                   height: 8,
  //                   width: 60,
  //                   decoration: BoxDecoration(
  //                     color: Colors.grey[700],
  //                     borderRadius: BorderRadius.circular(4),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }
}
