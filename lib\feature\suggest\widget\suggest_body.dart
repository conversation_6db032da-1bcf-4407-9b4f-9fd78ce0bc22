import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:movie_proj/core/const.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/my_text.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/core/service/service.dart';
import 'package:movie_proj/core/service/enums.dart';
import 'package:movie_proj/feature/details/details_screen.dart';
import 'package:movie_proj/feature/home/<USER>/movie_model.dart';
import 'package:movie_proj/feature/suggest/manage/top_rated_cubit.dart';
import 'package:movie_proj/feature/suggest/manage/suggest_search_cubit.dart';

class SuggestBody extends StatefulWidget {
  const SuggestBody({super.key});

  @override
  State<SuggestBody> createState() => _SuggestBodyState();
}

class _SuggestBodyState extends State<SuggestBody> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounceTimer;
  final Set<MovieModel> _selectedMovies = {};

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<TopRatedCubit>().refreshTopRated();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  MyText.forYourTaste,
                  style: MyStyles.title24White700.copyWith(fontSize: 24),
                ),
              ),
              vSpace(16),
              Center(
                child: Text(
                  MyText.choseFiveMovies,
                  style: MyStyles.title24White400.copyWith(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ),
              vSpace(30),
              _buildSearchSection(),
              vSpace(20),
              _buildSelectedMoviesSection(),
              vSpace(30),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Enhanced search bar with gradient and shadow
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                MyColors.secondaryColor,
                MyColors.secondaryColor.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.grey[600]!.withOpacity(0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: TextField(
            controller: _searchController,
            style: MyStyles.title24White400.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              hintText: 'Search for your favorite movies...',
              hintStyle: MyStyles.title24White400.copyWith(
                fontSize: 16,
                color: Colors.grey[400],
                fontWeight: FontWeight.w400,
              ),
              prefixIcon: Container(
                padding: const EdgeInsets.all(12),
                child: Icon(
                  Icons.search_rounded,
                  color: Colors.blue[300],
                  size: 24,
                ),
              ),
              suffixIcon: _searchController.text.isNotEmpty
                  ? Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: IconButton(
                        icon: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.grey[600]!.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Icon(
                            Icons.clear_rounded,
                            color: Colors.white70,
                            size: 16,
                          ),
                        ),
                        onPressed: () {
                          _searchController.clear();
                          context.read<SuggestSearchCubit>().clearSearch();
                          setState(() {});
                        },
                      ),
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 16,
              ),
            ),
            onChanged: (value) {
              setState(() {});
              if (value.trim().isNotEmpty) {
                _debounceTimer?.cancel();
                _debounceTimer = Timer(const Duration(milliseconds: 600), () {
                  if (mounted) {
                    context.read<SuggestSearchCubit>().searchMovies(value);
                  }
                });
              } else {
                context.read<SuggestSearchCubit>().clearSearch();
              }
            },
          ),
        ),
        vSpace(20),
        _buildSearchResults(),
      ],
    );
  }

  Widget _buildSearchResults() {
    return BlocBuilder<SuggestSearchCubit, SuggestSearchState>(
      builder: (context, state) {
        if (state is SuggestSearchInitial) {
          return const SizedBox.shrink();
        }

        if (state is SuggestSearchLoading) {
          return Container(
            height: 100,
            alignment: Alignment.center,
            child: const CircularProgressIndicator(
              color: Colors.white54,
              strokeWidth: 2,
            ),
          );
        }

        if (state is SuggestSearchEmpty) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: Text(
                'No movies found for "${state.query}"',
                style: MyStyles.title24White400.copyWith(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ),
          );
        }

        if (state is SuggestSearchError) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Center(
              child: Text(
                'Error: ${state.message}',
                style: MyStyles.title24White400.copyWith(
                  fontSize: 14,
                  color: Colors.red[300],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          );
        }

        if (state is SuggestSearchLoaded) {
          return SizedBox(
            height: 240,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 4),
              itemCount: state.movies.length,
              itemBuilder: (context, index) {
                final movie = state.movies[index];
                final isSelected = _selectedMovies.contains(movie);
                final canSelect = _selectedMovies.length < 5 || isSelected;

                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  width: 140,
                  margin: const EdgeInsets.only(right: 16),
                  child: GestureDetector(
                    onTap: canSelect
                        ? () {
                            setState(() {
                              if (isSelected) {
                                _selectedMovies.remove(movie);
                              } else {
                                _selectedMovies.add(movie);
                              }
                            });
                          }
                        : null,
                    child: AnimatedScale(
                      scale: isSelected ? 1.05 : 1.0,
                      duration: const Duration(milliseconds: 200),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          gradient: LinearGradient(
                            colors: isSelected
                                ? [
                                    Colors.blue.withOpacity(0.3),
                                    Colors.blue.withOpacity(0.1),
                                  ]
                                : [
                                    Colors.grey[800]!.withOpacity(0.3),
                                    Colors.grey[900]!.withOpacity(0.1),
                                  ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          border: Border.all(
                            color: isSelected
                                ? Colors.blue
                                : canSelect
                                    ? Colors.grey[600]!.withOpacity(0.3)
                                    : Colors.grey[600]!,
                            width: isSelected ? 2.5 : 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: isSelected
                                  ? Colors.blue.withOpacity(0.3)
                                  : Colors.black.withOpacity(0.2),
                              blurRadius: isSelected ? 12 : 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Movie poster with overlay
                            Expanded(
                              flex: 3,
                              child: Stack(
                                children: [
                                  ClipRRect(
                                    borderRadius: const BorderRadius.vertical(
                                      top: Radius.circular(16),
                                    ),
                                    child: (movie.posterPath?.isNotEmpty ??
                                            false)
                                        ? Image.network(
                                            'https://image.tmdb.org/t/p/w300${movie.posterPath}',
                                            fit: BoxFit.cover,
                                            width: double.infinity,
                                            height: double.infinity,
                                            errorBuilder:
                                                (context, error, stackTrace) {
                                              return _buildPlaceholderImage();
                                            },
                                          )
                                        : _buildPlaceholderImage(),
                                  ),
                                  // Selection overlay
                                  if (isSelected)
                                    Positioned.fill(
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              const BorderRadius.vertical(
                                            top: Radius.circular(16),
                                          ),
                                          gradient: LinearGradient(
                                            colors: [
                                              Colors.blue.withOpacity(0.3),
                                              Colors.transparent,
                                            ],
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                          ),
                                        ),
                                      ),
                                    ),
                                  // Selection check mark
                                  if (isSelected)
                                    Positioned(
                                      top: 8,
                                      right: 8,
                                      child: Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: const BoxDecoration(
                                          color: Colors.blue,
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black26,
                                              blurRadius: 4,
                                              offset: Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: const Icon(
                                          Icons.check_rounded,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  // Max limit indicator
                                  if (!canSelect && !isSelected)
                                    Positioned.fill(
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              const BorderRadius.vertical(
                                            top: Radius.circular(16),
                                          ),
                                          color: Colors.black.withOpacity(0.7),
                                        ),
                                        child: const Center(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.block_rounded,
                                                color: Colors.white70,
                                                size: 24,
                                              ),
                                              SizedBox(height: 4),
                                              Text(
                                                'Max 5',
                                                style: TextStyle(
                                                  color: Colors.white70,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                            // Movie info
                            Expanded(
                              flex: 1,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 6),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Flexible(
                                      child: Text(
                                        movie.title ?? 'Unknown Title',
                                        style:
                                            MyStyles.title24White400.copyWith(
                                          fontSize: 11,
                                          fontWeight: FontWeight.w600,
                                          height: 1.1,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.star_rounded,
                                          color: Colors.amber[600],
                                          size: 11,
                                        ),
                                        const SizedBox(width: 2),
                                        Text(
                                          (movie.voteAverage ?? 0.0)
                                              .toStringAsFixed(1),
                                          style:
                                              MyStyles.title24White400.copyWith(
                                            fontSize: 10,
                                            color: Colors.amber[600],
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        const Spacer(),
                                        if (movie.releaseDate?.isNotEmpty ??
                                            false)
                                          Text(
                                            movie.releaseDate!.substring(0, 4),
                                            style: MyStyles.title24White400
                                                .copyWith(
                                              fontSize: 9,
                                              color: Colors.grey[400],
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildSelectedMoviesSection() {
    if (_selectedMovies.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.withOpacity(0.1),
            Colors.purple.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.blue.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.favorite_rounded,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Your Selection',
                        style: MyStyles.title24White700.copyWith(fontSize: 18),
                      ),
                      Text(
                        '${_selectedMovies.length} of 5 movies selected',
                        style: MyStyles.title24White400.copyWith(
                          fontSize: 12,
                          color: Colors.grey[400],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              if (_selectedMovies.isNotEmpty)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.red.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: TextButton.icon(
                    onPressed: () {
                      setState(() {
                        _selectedMovies.clear();
                      });
                    },
                    icon: const Icon(
                      Icons.clear_all_rounded,
                      color: Colors.red,
                      size: 16,
                    ),
                    label: Text(
                      'Clear All',
                      style: MyStyles.title24White400.copyWith(
                        fontSize: 12,
                        color: Colors.red,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          ),
          vSpace(20),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 4),
              itemCount: _selectedMovies.length,
              itemBuilder: (context, index) {
                final movie = _selectedMovies.elementAt(index);
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: 130,
                  margin: const EdgeInsets.only(right: 16),
                  child: GestureDetector(
                    onTap: () => _navigateToMovieDetails(context, movie),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: LinearGradient(
                          colors: [
                            Colors.blue.withOpacity(0.2),
                            Colors.blue.withOpacity(0.05),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        border: Border.all(
                          color: Colors.blue.withOpacity(0.4),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withOpacity(0.2),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 3,
                            child: Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: const BorderRadius.vertical(
                                    top: Radius.circular(16),
                                  ),
                                  child: (movie.posterPath?.isNotEmpty ?? false)
                                      ? Image.network(
                                          'https://image.tmdb.org/t/p/w300${movie.posterPath}',
                                          fit: BoxFit.cover,
                                          width: double.infinity,
                                          height: double.infinity,
                                          errorBuilder:
                                              (context, error, stackTrace) {
                                            return _buildPlaceholderImage();
                                          },
                                        )
                                      : _buildPlaceholderImage(),
                                ),
                                // Remove button
                                Positioned(
                                  top: 8,
                                  right: 8,
                                  child: GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _selectedMovies.remove(movie);
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(6),
                                      decoration: BoxDecoration(
                                        color: Colors.red.withOpacity(0.9),
                                        shape: BoxShape.circle,
                                        boxShadow: const [
                                          BoxShadow(
                                            color: Colors.black26,
                                            blurRadius: 4,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: const Icon(
                                        Icons.close_rounded,
                                        color: Colors.white,
                                        size: 14,
                                      ),
                                    ),
                                  ),
                                ),
                                // Selection number badge
                                Positioned(
                                  top: 8,
                                  left: 8,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.blue,
                                      borderRadius: BorderRadius.circular(12),
                                      boxShadow: const [
                                        BoxShadow(
                                          color: Colors.black26,
                                          blurRadius: 4,
                                          offset: Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Text(
                                      '${index + 1}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Movie info
                          Expanded(
                            flex: 1,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 6),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Flexible(
                                    child: Text(
                                      movie.title ?? 'Unknown Title',
                                      style: MyStyles.title24White400.copyWith(
                                        fontSize: 11,
                                        fontWeight: FontWeight.w600,
                                        height: 1.1,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.star_rounded,
                                        color: Colors.amber[600],
                                        size: 11,
                                      ),
                                      const SizedBox(width: 2),
                                      Text(
                                        (movie.voteAverage ?? 0.0)
                                            .toStringAsFixed(1),
                                        style:
                                            MyStyles.title24White400.copyWith(
                                          fontSize: 10,
                                          color: Colors.amber[600],
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          vSpace(24),
          // Get recommendations button
          if (_selectedMovies.isNotEmpty)
            Center(
              child: Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colors.blue, Colors.purple],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: ElevatedButton.icon(
                  onPressed: () {
                    _showRecommendations();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shadowColor: Colors.transparent,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  icon: const Icon(
                    Icons.auto_awesome_rounded,
                    size: 20,
                  ),
                  label: Text(
                    'Get Smart Recommendations',
                    style: MyStyles.title24White700.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey[800]!,
            Colors.grey[900]!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.movie_creation_outlined,
            color: Colors.grey[600],
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            'No Image',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToMovieDetails(BuildContext context, MovieModel movie) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DetailsScreen(movie: movie),
      ),
    );
  }

  void _showRecommendations() async {
    if (_selectedMovies.isEmpty) return;

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: MyColors.secondaryColor,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(color: Colors.blue),
            vSpace(16),
            Text(
              'Analyzing your taste...',
              style: MyStyles.title24White400.copyWith(fontSize: 14),
            ),
          ],
        ),
      ),
    );

    try {
      // Get recommendations based on selected movies
      final recommendations =
          await _getRecommendations(_selectedMovies.toList());

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Show recommendations dialog
      if (mounted) {
        _showRecommendationsDialog(recommendations);
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.pop(context);

      // Show error dialog
      if (mounted) {
        _showErrorDialog('Failed to get recommendations: $e');
      }
    }
  }

  Future<List<MovieModel>> _getRecommendations(
      List<MovieModel> selectedMovies) async {
    final apiService = ApiService();
    final Set<MovieModel> allCandidates = {};

    // تحليل الأفلام المختارة لفهم تفضيلات المستخدم
    final preferredGenres = _analyzeGenres(selectedMovies);
    final averageRating = _calculateAverageRating(selectedMovies);
    final preferredYearRange = _analyzeYearRange(selectedMovies);

    print('تفضيلات المستخدم:');
    print('الأنواع المفضلة: $preferredGenres');
    print('متوسط التقييم: $averageRating');
    print('نطاق السنوات: $preferredYearRange');

    // الحصول على أفلام مشابهة لكل فيلم مختار
    for (final movie in selectedMovies) {
      try {
        if (movie.id != null) {
          // أفلام مشابهة مباشرة
          final similarMovies = await apiService.getMovieData(
            MovieType.similar,
            movieID: movie.id!,
          );
          allCandidates.addAll(similarMovies);

          // أفلام موصى بها بناءً على هذا الفيلم
          try {
            final recommendedMovies = await _getMovieRecommendations(movie.id!);
            allCandidates.addAll(recommendedMovies);
          } catch (e) {
            print('خطأ في الحصول على توصيات للفيلم ${movie.title}: $e');
          }
        }
      } catch (e) {
        print('خطأ في الحصول على أفلام مشابهة للفيلم ${movie.title}: $e');
      }
    }

    // الحصول على أفلام إضافية من نفس الأنواع المفضلة
    for (final genreId in preferredGenres.take(3)) {
      try {
        final genreMovies = await _getMoviesByGenre(genreId);
        allCandidates.addAll(genreMovies);
      } catch (e) {
        print('خطأ في الحصول على أفلام النوع $genreId: $e');
      }
    }

    // إزالة الأفلام المختارة من المرشحين
    final selectedIds = selectedMovies.map((m) => m.id).toSet();
    final filteredCandidates = allCandidates
        .where((movie) => !selectedIds.contains(movie.id))
        .toList();

    // تصفية وتسجيل الأفلام المرشحة بناءً على التفضيلات
    final scoredMovies = <MovieModel, double>{};

    for (final movie in filteredCandidates) {
      final score = _calculateAdvancedMovieScore(
        movie,
        preferredGenres,
        averageRating,
        preferredYearRange,
      );

      // قبول الأفلام التي تحصل على نقاط جيدة فقط
      if (score >= 2.0) {
        scoredMovies[movie] = score;
      }
    }

    // ترتيب الأفلام حسب النقاط وإرجاع أفضل 5
    final sortedMovies = scoredMovies.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final topRecommendations =
        sortedMovies.take(5).map((entry) => entry.key).toList();

    print('تم العثور على ${topRecommendations.length} توصيات عالية الجودة');

    return topRecommendations;
  }

  // دوال مساعدة للحصول على أفلام إضافية
  Future<List<MovieModel>> _getMovieRecommendations(int movieId) async {
    try {
      final response = await http.get(
        Uri.parse(
          'https://api.themoviedb.org/3/movie/$movieId/recommendations?api_key=$tmdbApiKey',
        ),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['results'] as List)
            .map((movie) => MovieModel.fromJson(movie))
            .toList();
      }
    } catch (e) {
      print('خطأ في الحصول على توصيات الفيلم $movieId: $e');
    }
    return [];
  }

  Future<List<MovieModel>> _getMoviesByGenre(int genreId) async {
    try {
      final response = await http.get(
        Uri.parse(
          'https://api.themoviedb.org/3/discover/movie?api_key=$tmdbApiKey&with_genres=$genreId&sort_by=vote_average.desc&vote_count.gte=100',
        ),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['results'] as List)
            .map((movie) => MovieModel.fromJson(movie))
            .take(10) // أخذ أفضل 10 أفلام فقط
            .toList();
      }
    } catch (e) {
      print('خطأ في الحصول على أفلام النوع $genreId: $e');
    }
    return [];
  }

  // دالة تسجيل متقدمة للأفلام
  double _calculateAdvancedMovieScore(
    MovieModel movie,
    List<int> preferredGenres,
    double averageRating,
    Map<String, int> yearRange,
  ) {
    double score = 0;

    // نقاط مطابقة الأنواع (0-5 نقاط)
    if (movie.genreIds != null && preferredGenres.isNotEmpty) {
      final matchingGenres = movie.genreIds!
          .where((genreId) => preferredGenres.contains(genreId))
          .length;
      score += matchingGenres * 1.5; // 1.5 نقطة لكل نوع مطابق
    }

    // نقاط التقييم المشابه (0-3 نقاط)
    if (movie.voteAverage != null && averageRating > 0) {
      final ratingDiff = (movie.voteAverage! - averageRating).abs();
      if (ratingDiff <= 0.5) {
        score += 3; // تقييم مطابق جداً
      } else if (ratingDiff <= 1.0) {
        score += 2; // تقييم مطابق
      } else if (ratingDiff <= 1.5) {
        score += 1; // تقييم قريب
      }
    }

    // نقاط السنة (0-2 نقطة)
    if (movie.releaseDate?.isNotEmpty ?? false) {
      try {
        final year = int.parse(movie.releaseDate!.substring(0, 4));
        final minYear = yearRange['min'] ?? 2000;
        final maxYear = yearRange['max'] ?? DateTime.now().year;

        if (year >= minYear && year <= maxYear) {
          score += 2; // ضمن النطاق المفضل
        } else if ((year - maxYear).abs() <= 3 || (minYear - year).abs() <= 3) {
          score += 1; // قريب من النطاق المفضل
        }
      } catch (e) {
        // تجاهل الأخطاء في التاريخ
      }
    }

    // نقاط الشعبية والجودة (0-2 نقطة)
    if (movie.popularity != null && movie.voteCount != null) {
      if (movie.voteCount! >= 1000 && movie.voteAverage! >= 7.0) {
        score += 2; // فيلم عالي الجودة ومشهور
      } else if (movie.voteCount! >= 500 && movie.voteAverage! >= 6.5) {
        score += 1; // فيلم جيد
      }
    }

    return score;
  }

  // دوال التحليل المحسنة للحصول على تفضيلات المستخدم
  List<int> _analyzeGenres(List<MovieModel> movies) {
    final genreCount = <int, int>{};

    // عد تكرار كل نوع في الأفلام المختارة
    for (final movie in movies) {
      if (movie.genreIds != null) {
        for (final genreId in movie.genreIds!) {
          genreCount[genreId] = (genreCount[genreId] ?? 0) + 1;
        }
      }
    }

    // ترتيب الأنواع حسب التكرار وإرجاع الأكثر شيوعاً
    final sortedGenres = genreCount.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedGenres
        .map((entry) => entry.key)
        .take(5)
        .toList(); // أفضل 5 أنواع
  }

  double _calculateAverageRating(List<MovieModel> movies) {
    if (movies.isEmpty) return 7.0; // تقييم افتراضي جيد

    double totalRating = 0;
    int validRatings = 0;

    for (final movie in movies) {
      if (movie.voteAverage != null && movie.voteAverage! > 0) {
        totalRating += movie.voteAverage!;
        validRatings++;
      }
    }

    return validRatings > 0 ? totalRating / validRatings : 7.0;
  }

  Map<String, int> _analyzeYearRange(List<MovieModel> movies) {
    if (movies.isEmpty) {
      return {
        'min': DateTime.now().year - 10,
        'max': DateTime.now().year,
      };
    }

    final years = <int>[];

    for (final movie in movies) {
      if (movie.releaseDate?.isNotEmpty ?? false) {
        try {
          final year = int.parse(movie.releaseDate!.substring(0, 4));
          years.add(year);
        } catch (e) {
          // تجاهل التواريخ غير الصحيحة
        }
      }
    }

    if (years.isEmpty) {
      return {
        'min': DateTime.now().year - 10,
        'max': DateTime.now().year,
      };
    }

    years.sort();
    final minYear = years.first;
    final maxYear = years.last;

    // توسيع النطاق قليلاً للحصول على تنوع أكبر
    return {
      'min': (minYear - 3).clamp(1990, DateTime.now().year),
      'max': (maxYear + 3).clamp(1990, DateTime.now().year),
    };
    final List<int> years = [];

    for (final movie in movies) {
      if (movie.releaseDate != null && movie.releaseDate!.isNotEmpty) {
        try {
          final year = int.parse(movie.releaseDate!.substring(0, 4));
          years.add(year);
        } catch (e) {
          // Ignore invalid dates
        }
      }
    }

    if (years.isEmpty) {
      return {'min': 2010, 'max': DateTime.now().year};
    }

    years.sort();
    final minYear = years.first;
    final maxYear = years.last;

    // Expand range slightly for variety
    return {
      'min': (minYear - 5).clamp(1990, DateTime.now().year),
      'max': (maxYear + 5).clamp(1990, DateTime.now().year),
    };
  }

  List<MovieModel> _filterAndScoreMovies(
    List<MovieModel> movies,
    List<int> preferredGenres,
    double averageRating,
    Map<String, int> yearRange,
  ) {
    return movies.where((movie) {
      // Filter by rating (within 1.5 points of average)
      if (movie.voteAverage != null) {
        final ratingDiff = (movie.voteAverage! - averageRating).abs();
        if (ratingDiff > 1.5) return false;
      }

      // Filter by year range
      if (movie.releaseDate != null && movie.releaseDate!.isNotEmpty) {
        try {
          final year = int.parse(movie.releaseDate!.substring(0, 4));
          if (year < yearRange['min']! || year > yearRange['max']!) {
            return false;
          }
        } catch (e) {
          // Include movies with invalid dates
        }
      }

      // Must have at least one preferred genre
      if (movie.genreIds != null && preferredGenres.isNotEmpty) {
        return movie.genreIds!
            .any((genreId) => preferredGenres.contains(genreId));
      }

      return true;
    }).toList();
  }

  double _calculateMovieScore(
    MovieModel movie,
    List<int> preferredGenres,
    double averageRating,
  ) {
    double score = 0;

    // Genre matching score (0-3 points)
    if (movie.genreIds != null) {
      final matchingGenres = movie.genreIds!
          .where((genreId) => preferredGenres.contains(genreId))
          .length;
      score += matchingGenres.toDouble();
    }

    // Rating similarity score (0-2 points)
    if (movie.voteAverage != null) {
      final ratingDiff = (movie.voteAverage! - averageRating).abs();
      score += (2 - ratingDiff).clamp(0, 2);
    }

    // Popularity bonus (0-1 point)
    if (movie.popularity != null) {
      score += (movie.popularity! / 100).clamp(0, 1);
    }

    return score;
  }

  void _showRecommendationsDialog(List<MovieModel> recommendations) {
    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.8),
      builder: (context) => AnimatedScale(
        scale: 1.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutBack,
        child: Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(16),
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height * 0.85,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF1a1a2e),
                  const Color(0xFF16213e),
                  const Color(0xFF0f3460),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: Colors.blue.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.5),
                  blurRadius: 30,
                  offset: const Offset(0, 15),
                ),
                BoxShadow(
                  color: Colors.blue.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                // Enhanced Header
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.blue.withOpacity(0.3),
                        Colors.purple.withOpacity(0.2),
                        Colors.transparent,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(24),
                    ),
                  ),
                  child: Row(
                    children: [
                      // Animated icon
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Colors.blue, Colors.purple],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.blue.withOpacity(0.3),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.auto_awesome_rounded,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Smart Recommendations',
                              style: MyStyles.title24White700.copyWith(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Curated based on your ${_selectedMovies.length} selected movies',
                              style: MyStyles.title24White400.copyWith(
                                fontSize: 14,
                                color: Colors.blue[200],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Close button
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.red.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(
                            Icons.close_rounded,
                            color: Colors.red,
                            size: 24,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Content Section
                Expanded(
                  child: recommendations.isEmpty
                      ? _buildEmptyRecommendations()
                      : _buildRecommendationsList(recommendations),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyRecommendations() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.grey[800]!.withOpacity(0.3),
                  Colors.grey[900]!.withOpacity(0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.grey[600]!.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.search_off_rounded,
                  color: Colors.grey[500],
                  size: 80,
                ),
                const SizedBox(height: 20),
                Text(
                  'No Recommendations Found',
                  style: MyStyles.title24White700.copyWith(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Try selecting different movies with\nmore popular titles or varied genres',
                  style: MyStyles.title24White400.copyWith(
                    fontSize: 16,
                    color: Colors.grey[400],
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.blue.withOpacity(0.2),
                        Colors.purple.withOpacity(0.1),
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: Colors.blue.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.lightbulb_outline_rounded,
                        color: Colors.blue[300],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Tip: Select popular movies for better results',
                        style: MyStyles.title24White400.copyWith(
                          fontSize: 14,
                          color: Colors.blue[300],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationsList(List<MovieModel> recommendations) {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: recommendations.length,
      itemBuilder: (context, index) {
        final movie = recommendations[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.grey[800]!.withOpacity(0.4),
                Colors.grey[900]!.withOpacity(0.2),
                Colors.black.withOpacity(0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.grey[600]!.withOpacity(0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                Navigator.pop(context);
                _navigateToMovieDetails(context, movie);
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Movie Poster
                    Container(
                      width: 80,
                      height: 120,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.4),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: (movie.posterPath?.isNotEmpty ?? false)
                            ? Image.network(
                                'https://image.tmdb.org/t/p/w300${movie.posterPath}',
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return _buildPlaceholderImage();
                                },
                              )
                            : _buildPlaceholderImage(),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Movie Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title
                          Text(
                            movie.title ?? 'Unknown Title',
                            style: MyStyles.title24White700.copyWith(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              height: 1.3,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          // Rating and Year
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.amber.withOpacity(0.3),
                                      Colors.orange.withOpacity(0.2),
                                    ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                  ),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.amber.withOpacity(0.4),
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.star_rounded,
                                      color: Colors.amber[300],
                                      size: 16,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      (movie.voteAverage ?? 0.0)
                                          .toStringAsFixed(1),
                                      style: MyStyles.title24White400.copyWith(
                                        fontSize: 14,
                                        color: Colors.amber[300],
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 12),
                              if (movie.releaseDate?.isNotEmpty ?? false)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Colors.blue.withOpacity(0.3),
                                        Colors.purple.withOpacity(0.2),
                                      ],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.blue.withOpacity(0.4),
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    movie.releaseDate!.substring(0, 4),
                                    style: MyStyles.title24White400.copyWith(
                                      fontSize: 14,
                                      color: Colors.blue[300],
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          // Overview (if available)
                          if (movie.overview?.isNotEmpty ?? false)
                            Text(
                              movie.overview!,
                              style: MyStyles.title24White400.copyWith(
                                fontSize: 14,
                                color: Colors.grey[300],
                                height: 1.4,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                    // Arrow Icon
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.blue.withOpacity(0.3),
                            Colors.purple.withOpacity(0.2),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.blue.withOpacity(0.4),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Icons.arrow_forward_ios_rounded,
                        color: Colors.blue[300],
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: MyColors.secondaryColor,
        title: Text(
          'Error',
          style: MyStyles.title24White700.copyWith(fontSize: 18),
        ),
        content: Text(
          message,
          style: MyStyles.title24White400.copyWith(fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'OK',
              style: MyStyles.title24White400.copyWith(
                fontSize: 14,
                color: Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // int _getCrossAxisCount(double width) {
  //   if (width <= 400) return 2;
  //   if (width <= 700) return 3;
  //   return 4;
  // }

  // double _getSpacing(double width) {
  //   if (width <= 400) return 8;
  //   if (width <= 700) return 12;
  //   return 16;
  // }

  // Widget _buildTopRatedCard(TopRatedItem item, double containerWidth) {
  //   final isSmallScreen = containerWidth <= 400;
  //   final isMediumScreen = containerWidth <= 700;

  //   return Container(
  //     decoration: BoxDecoration(
  //       color: MyColors.secondaryColor,
  //       borderRadius: BorderRadius.circular(10),
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Expanded(
  //           flex: 3,
  //           child: Container(
  //             width: double.infinity,
  //             decoration: BoxDecoration(
  //               borderRadius:
  //                   const BorderRadius.vertical(top: Radius.circular(10)),
  //             ),
  //             child: ClipRRect(
  //               borderRadius:
  //                   const BorderRadius.vertical(top: Radius.circular(10)),
  //               child: item.posterPath.isNotEmpty
  //                   ? Image.network(
  //                       'https://image.tmdb.org/t/p/w500${item.posterPath}',
  //                       fit: BoxFit.cover,
  //                       errorBuilder: (context, error, stackTrace) {
  //                         return _buildPlaceholderImage();
  //                       },
  //                       loadingBuilder: (context, child, loadingProgress) {
  //                         if (loadingProgress == null) return child;
  //                         return _buildLoadingImage();
  //                       },
  //                     )
  //                   : _buildPlaceholderImage(),
  //             ),
  //           ),
  //         ),
  //         Expanded(
  //           flex: 1,
  //           child: Padding(
  //             padding: EdgeInsets.all(isSmallScreen ? 8 : 10),
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Text(
  //                   item.title,
  //                   style: MyStyles.title24White400.copyWith(
  //                     fontSize: isSmallScreen ? 10 : (isMediumScreen ? 11 : 12),
  //                     fontWeight: FontWeight.bold,
  //                   ),
  //                   maxLines: 1,
  //                   overflow: TextOverflow.ellipsis,
  //                 ),
  //                 const SizedBox(height: 2),
  //                 Row(
  //                   children: [
  //                     Icon(
  //                       Icons.star,
  //                       color: Colors.amber,
  //                       size: isSmallScreen ? 12 : 14,
  //                     ),
  //                     const SizedBox(width: 2),
  //                     Text(
  //                       item.rating.toStringAsFixed(1),
  //                       style: MyStyles.title24White400.copyWith(
  //                         fontSize: isSmallScreen ? 8 : 10,
  //                         color: Colors.amber,
  //                       ),
  //                     ),
  //                     const SizedBox(width: 8),
  //                     Container(
  //                       padding: const EdgeInsets.symmetric(
  //                           horizontal: 4, vertical: 1),
  //                       decoration: BoxDecoration(
  //                         color: item.isMovie ? Colors.blue : Colors.green,
  //                         borderRadius: BorderRadius.circular(4),
  //                       ),
  //                       child: Text(
  //                         item.isMovie ? 'Movie' : 'TV',
  //                         style: MyStyles.title24White400.copyWith(
  //                           fontSize: isSmallScreen ? 6 : 8,
  //                           color: Colors.white,
  //                         ),
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // Widget _buildLoadingCard(double containerWidth) {
  //   final isSmallScreen = containerWidth <= 400;

  //   return Container(
  //     decoration: BoxDecoration(
  //       color: MyColors.secondaryColor,
  //       borderRadius: BorderRadius.circular(10),
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Expanded(
  //           flex: 3,
  //           child: Container(
  //             width: double.infinity,
  //             decoration: BoxDecoration(
  //               borderRadius:
  //                   const BorderRadius.vertical(top: Radius.circular(10)),
  //               color: Colors.grey[800],
  //             ),
  //             child: const Center(
  //               child: CircularProgressIndicator(
  //                 color: Colors.white54,
  //                 strokeWidth: 2,
  //               ),
  //             ),
  //           ),
  //         ),
  //         Expanded(
  //           flex: 1,
  //           child: Padding(
  //             padding: EdgeInsets.all(isSmallScreen ? 8 : 10),
  //             child: Column(
  //               crossAxisAlignment: CrossAxisAlignment.start,
  //               children: [
  //                 Container(
  //                   height: 12,
  //                   width: double.infinity,
  //                   decoration: BoxDecoration(
  //                     color: Colors.grey[700],
  //                     borderRadius: BorderRadius.circular(4),
  //                   ),
  //                 ),
  //                 const SizedBox(height: 4),
  //                 Container(
  //                   height: 8,
  //                   width: 60,
  //                   decoration: BoxDecoration(
  //                     color: Colors.grey[700],
  //                     borderRadius: BorderRadius.circular(4),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }
}
