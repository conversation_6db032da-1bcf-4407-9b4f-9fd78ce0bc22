import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/service/service.dart';
import 'package:movie_proj/feature/home/<USER>/movie_model.dart';

// States
abstract class SuggestSearchState {}

class SuggestSearchInitial extends SuggestSearchState {}

class SuggestSearchLoading extends SuggestSearchState {}

class SuggestSearchLoaded extends SuggestSearchState {
  final List<MovieModel> movies;
  final String query;

  SuggestSearchLoaded({
    required this.movies,
    required this.query,
  });
}

class SuggestSearchEmpty extends SuggestSearchState {
  final String query;

  SuggestSearchEmpty(this.query);
}

class SuggestSearchError extends SuggestSearchState {
  final String message;

  SuggestSearchError(this.message);
}

// Cubit
class SuggestSearchCubit extends Cubit<SuggestSearchState> {
  final ApiService _apiService;

  SuggestSearchCubit(this._apiService) : super(SuggestSearchInitial());

  Future<void> searchMovies(String query) async {
    if (query.trim().isEmpty) {
      emit(SuggestSearchInitial());
      return;
    }

    try {
      if (kDebugMode) {
        print('🔍 SuggestSearchCubit: Searching for "$query"');
      }

      emit(SuggestSearchLoading());

      final movies = await _apiService.searchMovies(query);

      if (kDebugMode) {
        print('🔍 SuggestSearchCubit: Found ${movies.length} movies');
      }

      if (movies.isEmpty) {
        emit(SuggestSearchEmpty(query));
        return;
      }

      // Limit to first 20 results for better performance
      final limitedMovies = movies.take(20).toList();

      emit(SuggestSearchLoaded(
        movies: limitedMovies,
        query: query,
      ));
    } catch (e) {
      if (kDebugMode) {
        print('🔍 SuggestSearchCubit: Error searching movies: $e');
      }
      emit(SuggestSearchError('Failed to search movies: $e'));
    }
  }

  void clearSearch() {
    emit(SuggestSearchInitial());
  }
}
