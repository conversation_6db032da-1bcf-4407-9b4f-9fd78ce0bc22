import 'package:flutter/foundation.dart';
import 'package:movie_proj/core/service/service.dart';
import 'package:movie_proj/feature/suggest/manage/suggest_search_cubit.dart';

/// Test function to verify Suggest Search integration
Future<void> testSuggestSearchIntegration() async {
  try {
    debugPrint('🔍 Testing Suggest Search Integration...');

    // Test the service
    final service = ApiService();

    // Test search functionality
    final movies = await service.searchMovies('Marvel');
    debugPrint('✅ Search Service working!');
    debugPrint('🎬 Number of movies found: ${movies.length}');

    if (movies.isNotEmpty) {
      final firstMovie = movies.first;
      debugPrint('🎬 First movie: ${firstMovie.title}');
      debugPrint('🎬 Poster path: ${firstMovie.posterPath}');
      debugPrint('🎬 Rating: ${firstMovie.voteAverage}');
      debugPrint('🎬 Release date: ${firstMovie.releaseDate}');
    }

    // Test the cubit
    final cubit = SuggestSearchCubit(service);
    
    // Test search
    debugPrint('🔍 Testing SuggestSearchCubit...');
    await cubit.searchMovies('Spider-Man');
    
    final state = cubit.state;
    if (state is SuggestSearchLoaded) {
      debugPrint('✅ SuggestSearchCubit working!');
      debugPrint('🎬 Found ${state.movies.length} movies for "${state.query}"');
      
      if (state.movies.isNotEmpty) {
        final firstResult = state.movies.first;
        debugPrint('🎬 First result: ${firstResult.title}');
        debugPrint('🎬 Rating: ${firstResult.voteAverage}');
      }
    } else if (state is SuggestSearchError) {
      debugPrint('❌ SuggestSearchCubit error: ${state.message}');
    } else if (state is SuggestSearchEmpty) {
      debugPrint('📭 No results found for "${state.query}"');
    }

    // Test clear search
    cubit.clearSearch();
    final clearedState = cubit.state;
    if (clearedState is SuggestSearchInitial) {
      debugPrint('✅ Clear search working!');
    }

    debugPrint('🎉 Suggest Search Integration test completed successfully!');
  } catch (e) {
    debugPrint('❌ Suggest Search Integration test failed: $e');
  }
}
