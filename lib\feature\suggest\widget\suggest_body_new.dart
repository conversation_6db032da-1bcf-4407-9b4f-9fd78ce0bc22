import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/const.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/core/service/service.dart';
import 'package:movie_proj/core/service/enums.dart';
import 'package:movie_proj/feature/details/details_screen.dart';
import 'package:movie_proj/feature/home/<USER>/movie_model.dart';
import 'package:movie_proj/feature/suggest/manage/suggest_search_cubit.dart';

class SuggestBody extends StatefulWidget {
  const SuggestBody({super.key});

  @override
  State<SuggestBody> createState() => _SuggestBodyState();
}

class _SuggestBodyState extends State<SuggestBody> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounceTimer;
  final Set<MovieModel> _selectedMovies = {};

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // Search change handler
  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query.isNotEmpty) {
        context.read<SuggestSearchCubit>().searchMovies(query);
      } else {
        context.read<SuggestSearchCubit>().clearSearch();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MyColors.primaryColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              vSpace(20),
              // For Your Taste Section
              _buildForYourTasteSection(),
              vSpace(40),
              // Recommendations Section
              if (_selectedMovies.isNotEmpty) _buildRecommendationsSection(),
            ],
          ),
        ),
      ),
    );
  }

  // For Your Taste Section
  Widget _buildForYourTasteSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'For Your Taste',
            style: MyStyles.title24White700.copyWith(
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
          vSpace(8),
          // Subtitle
          Text(
            'Choose 5 Movies or TV Series To Have A Recommend According On Your Taste',
            style: MyStyles.title24White400.copyWith(
              fontSize: 16,
              color: Colors.grey[400],
            ),
          ),
          vSpace(32),
          // Search Bar
          _buildSearchBar(),
          vSpace(24),
          // Selected Movies Grid
          _buildSelectedMoviesGrid(),
        ],
      ),
    );
  }

  // Search Bar Widget
  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: MyColors.secondaryColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[700]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // All Dropdown
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border(
                right: BorderSide(color: Colors.grey[700]!, width: 1),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'All',
                  style: MyStyles.title24White400.copyWith(fontSize: 14),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.grey[400],
                  size: 20,
                ),
              ],
            ),
          ),
          // Search Field
          Expanded(
            child: TextField(
              controller: _searchController,
              style: MyStyles.title24White400.copyWith(fontSize: 14),
              decoration: InputDecoration(
                hintText: 'Search MOVIE BOX',
                hintStyle: MyStyles.title24White400.copyWith(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: _onSearchChanged,
            ),
          ),
          // Search Icon
          Container(
            padding: const EdgeInsets.all(12),
            child: Icon(
              Icons.search,
              color: Colors.grey[400],
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  // Selected Movies Grid
  Widget _buildSelectedMoviesGrid() {
    return Column(
      children: [
        // First Row - Selected Movies
        SizedBox(
          height: 200,
          child: Row(
            children: [
              // Selected Movies
              ...List.generate(
                3,
                (index) => Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(right: 16),
                    child: _selectedMovies.length > index
                        ? _buildMovieCard(
                            _selectedMovies.elementAt(index), true)
                        : _buildEmptyMovieSlot(),
                  ),
                ),
              ),
            ],
          ),
        ),
        vSpace(16),
        // Second Row - More Selected Movies
        SizedBox(
          height: 200,
          child: Row(
            children: [
              // More Selected Movies
              ...List.generate(
                2,
                (index) => Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(right: 16),
                    child: _selectedMovies.length > (index + 3)
                        ? _buildMovieCard(
                            _selectedMovies.elementAt(index + 3), true)
                        : _buildEmptyMovieSlot(),
                  ),
                ),
              ),
              // Empty space to match layout
              const Expanded(child: SizedBox()),
            ],
          ),
        ),
        vSpace(24),
        // Search Results
        _buildSearchResults(),
      ],
    );
  }

  // Recommendations Section
  Widget _buildRecommendationsSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            'We Recommend This Movies / Tv Series',
            style: MyStyles.title24White700.copyWith(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          vSpace(24),
          // Recommendations Grid
          _buildRecommendationsGrid(),
        ],
      ),
    );
  }

  // Movie Card Widget
  Widget _buildMovieCard(MovieModel movie, bool isSelected) {
    return GestureDetector(
      onTap: () {
        if (isSelected) {
          setState(() {
            _selectedMovies.remove(movie);
          });
        } else {
          if (_selectedMovies.length < 5) {
            setState(() {
              _selectedMovies.add(movie);
            });
          }
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          image: movie.posterPath != null
              ? DecorationImage(
                  image: NetworkImage(
                    '$kmoviedbImageURL${movie.posterPath}',
                  ),
                  fit: BoxFit.cover,
                )
              : null,
          color: movie.posterPath == null ? MyColors.secondaryColor : null,
        ),
        child: Stack(
          children: [
            // Movie Title at bottom
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Text(
                  movie.title ?? 'Unknown',
                  style: MyStyles.title24White400.copyWith(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
            // Selection indicator
            if (isSelected)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Empty Movie Slot Widget
  Widget _buildEmptyMovieSlot() {
    return Container(
      decoration: BoxDecoration(
        color: MyColors.secondaryColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[700]!,
          width: 2,
          style: BorderStyle.solid,
        ),
      ),
      child: const Center(
        child: Icon(
          Icons.add,
          color: Colors.grey,
          size: 40,
        ),
      ),
    );
  }

  // Search Results Widget
  Widget _buildSearchResults() {
    return BlocBuilder<SuggestSearchCubit, SuggestSearchState>(
      builder: (context, state) {
        if (state is SuggestSearchLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is SuggestSearchLoaded) {
          final movies = state.movies;
          if (movies.isEmpty) {
            return const Center(
              child: Text(
                'No movies found',
                style: TextStyle(color: Colors.grey),
              ),
            );
          }

          return SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: movies.length,
              itemBuilder: (context, index) {
                final movie = movies[index];
                final isSelected = _selectedMovies.contains(movie);
                return Container(
                  width: 140,
                  margin: const EdgeInsets.only(right: 12),
                  child: _buildMovieCard(movie, isSelected),
                );
              },
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  // Recommendations Grid
  Widget _buildRecommendationsGrid() {
    return FutureBuilder<List<MovieModel>>(
      future: _getRecommendations(_selectedMovies.toList()),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Text(
              'No recommendations available',
              style: TextStyle(color: Colors.grey),
            ),
          );
        }

        final recommendations = snapshot.data!;
        return SizedBox(
          height: 300,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: recommendations.length,
            itemBuilder: (context, index) {
              final movie = recommendations[index];
              return Container(
                width: 200,
                margin: const EdgeInsets.only(right: 16),
                child: _buildRecommendationCard(movie),
              );
            },
          ),
        );
      },
    );
  }

  // Recommendation Card
  Widget _buildRecommendationCard(MovieModel movie) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DetailsScreen(movie: movie),
          ),
        );
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Movie Poster
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                image: movie.posterPath != null
                    ? DecorationImage(
                        image: NetworkImage(
                          '$kmoviedbImageURL${movie.posterPath}',
                        ),
                        fit: BoxFit.cover,
                      )
                    : null,
                color:
                    movie.posterPath == null ? MyColors.secondaryColor : null,
              ),
              child: Stack(
                children: [
                  // Rating Badge
                  if (movie.voteAverage != null)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 12,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              movie.voteAverage!.toStringAsFixed(1),
                              style: MyStyles.title24White400.copyWith(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          vSpace(8),
          // Movie Info
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Country and Year
              Text(
                'USA, ${movie.releaseDate?.substring(0, 4) ?? '2024'}',
                style: MyStyles.title24White400.copyWith(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
              vSpace(4),
              // Title
              Text(
                movie.title ?? 'Unknown',
                style: MyStyles.title24White400.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              vSpace(4),
              // Genre
              Text(
                'Action, Adventure',
                style: MyStyles.title24White400.copyWith(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to get recommendations
  Future<List<MovieModel>> _getRecommendations(
      List<MovieModel> selectedMovies) async {
    if (selectedMovies.isEmpty) {
      return [];
    }

    try {
      final apiService = ApiService();
      final Set<MovieModel> allCandidates = {};

      // Get similar movies for each selected movie
      for (final movie in selectedMovies) {
        if (movie.id != null) {
          try {
            final similarMovies = await apiService.getMovieData(
              MovieType.similar,
              movieID: movie.id!,
            );
            allCandidates.addAll(similarMovies);
          } catch (e) {
            print('Error getting similar movies for ${movie.title}: $e');
          }
        }
      }

      // Remove duplicates and selected movies
      final selectedIds = selectedMovies.map((m) => m.id).toSet();
      final filteredCandidates = allCandidates
          .where((movie) => !selectedIds.contains(movie.id))
          .toList();

      // Return top 10 recommendations
      filteredCandidates.shuffle();
      return filteredCandidates.take(10).toList();
    } catch (e) {
      print('Error getting recommendations: $e');
      return [];
    }
  }
}
