import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/const.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/core/service/service.dart';
import 'package:movie_proj/core/service/enums.dart';
import 'package:movie_proj/feature/details/details_screen.dart';
import 'package:movie_proj/feature/home/<USER>/movie_model.dart';
import 'package:movie_proj/feature/suggest/manage/suggest_search_cubit.dart';

class SuggestBody extends StatefulWidget {
  const SuggestBody({super.key});

  @override
  State<SuggestBody> createState() => _SuggestBodyState();
}

class _SuggestBodyState extends State<SuggestBody> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounceTimer;
  final Set<MovieModel> _selectedMovies = {};

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // Search change handler
  void _onSearchChanged(String query) {
    setState(() {}); // Update UI for search icon animation
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query.isNotEmpty) {
        context.read<SuggestSearchCubit>().searchMovies(query);
      } else {
        context.read<SuggestSearchCubit>().clearSearch();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MyColors.primaryColor,
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              vSpace(20),
              // For Your Taste Section
              _buildForYourTasteSection(),
              vSpace(40),
              // Recommendations Section with animation
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 600),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 0.3),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeOutCubic,
                    )),
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  );
                },
                child: _selectedMovies.isNotEmpty
                    ? _buildRecommendationsSection()
                    : _buildEmptyRecommendationsState(),
              ),
              vSpace(40),
            ],
          ),
        ),
      ),
    );
  }

  // Empty Recommendations State
  Widget _buildEmptyRecommendationsState() {
    return Container(
      key: const ValueKey('empty'),
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey[800]!.withOpacity(0.3),
            Colors.grey[900]!.withOpacity(0.2),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey[700]!.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[700]!.withOpacity(0.3),
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              Icons.movie_filter_outlined,
              size: 48,
              color: Colors.grey[500],
            ),
          ),
          vSpace(20),
          Text(
            'Select Movies to Get Recommendations',
            style: MyStyles.title24White700.copyWith(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[400],
            ),
            textAlign: TextAlign.center,
          ),
          vSpace(8),
          Text(
            'Choose up to 5 movies or TV series above to discover personalized recommendations based on your taste.',
            style: MyStyles.title24White400.copyWith(
              fontSize: 14,
              color: Colors.grey[500],
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // For Your Taste Section
  Widget _buildForYourTasteSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            MyColors.primaryColor,
            MyColors.primaryColor.withOpacity(0.95),
          ],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with animation
            AnimatedContainer(
              duration: const Duration(milliseconds: 800),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title with gradient
                  ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      colors: [Colors.white, Colors.blue, Colors.purple],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ).createShader(bounds),
                    child: Text(
                      'For Your Taste',
                      style: MyStyles.title24White700.copyWith(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  vSpace(12),
                  // Subtitle with better styling
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'Choose 5 Movies or TV Series To Have A Recommend According On Your Taste',
                      style: MyStyles.title24White400.copyWith(
                        fontSize: 16,
                        color: Colors.grey[300],
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            vSpace(40),
            // Search Bar
            _buildSearchBar(),
            vSpace(32),
            // Selection Counter
            _buildSelectionCounter(),
            vSpace(24),
            // Selected Movies Grid
            _buildSelectedMoviesGrid(),
          ],
        ),
      ),
    );
  }

  // Selection Counter Widget
  Widget _buildSelectionCounter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.withOpacity(0.2),
            Colors.purple.withOpacity(0.2),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: Colors.blue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.movie_filter_outlined,
                  color: Colors.blue[300],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Selected Movies',
                    style: MyStyles.title24White700.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '${_selectedMovies.length} of 5 selected',
                    style: MyStyles.title24White400.copyWith(
                      fontSize: 12,
                      color: Colors.grey[400],
                    ),
                  ),
                ],
              ),
            ],
          ),
          // Progress indicator
          Container(
            width: 80,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: _selectedMovies.length / 5,
              child: Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colors.blue, Colors.purple],
                  ),
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Search Bar Widget
  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            MyColors.secondaryColor,
            MyColors.secondaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // All Dropdown with better styling
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue.withOpacity(0.2),
                  Colors.blue.withOpacity(0.1),
                ],
              ),
              border: Border(
                right: BorderSide(
                  color: Colors.blue.withOpacity(0.3),
                  width: 1,
                ),
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.tune_rounded,
                  color: Colors.blue[300],
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'All',
                  style: MyStyles.title24White400.copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[300],
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.keyboard_arrow_down_rounded,
                  color: Colors.blue[300],
                  size: 20,
                ),
              ],
            ),
          ),
          // Search Field with better styling
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: TextField(
                controller: _searchController,
                style: MyStyles.title24White400.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                decoration: InputDecoration(
                  hintText: 'Search for movies, TV shows...',
                  hintStyle: MyStyles.title24White400.copyWith(
                    fontSize: 16,
                    color: Colors.grey[400],
                    fontWeight: FontWeight.w400,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 16),
                ),
                onChanged: _onSearchChanged,
              ),
            ),
          ),
          // Search Icon with animation
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: _searchController.text.isNotEmpty
                  ? const LinearGradient(
                      colors: [Colors.blue, Colors.purple],
                    )
                  : null,
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: Icon(
              _searchController.text.isNotEmpty
                  ? Icons.search_rounded
                  : Icons.search_outlined,
              color: _searchController.text.isNotEmpty
                  ? Colors.white
                  : Colors.grey[400],
              size: 22,
            ),
          ),
        ],
      ),
    );
  }

  // Selected Movies Grid
  Widget _buildSelectedMoviesGrid() {
    return Column(
      children: [
        // Single Row - All Selected Movies (5 slots)
        SizedBox(
          height: 140, // Reduced height
          child: Row(
            children: [
              // All 5 movie slots in one row
              ...List.generate(
                5,
                (index) => Expanded(
                  child: Container(
                    margin: EdgeInsets.only(
                      right: index < 4 ? 12 : 0, // Reduced margin
                    ),
                    child: _selectedMovies.length > index
                        ? _buildMovieCard(
                            _selectedMovies.elementAt(index), true)
                        : _buildEmptyMovieSlot(),
                  ),
                ),
              ),
            ],
          ),
        ),
        vSpace(24),
        // Search Results
        _buildSearchResults(),
      ],
    );
  }

  // Recommendations Section
  Widget _buildRecommendationsSection() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 800),
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.purple.withOpacity(0.1),
            Colors.blue.withOpacity(0.1),
            Colors.indigo.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.purple.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Colors.purple, Colors.blue],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.purple.withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.auto_awesome_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [Colors.purple, Colors.blue, Colors.indigo],
                        ).createShader(bounds),
                        child: Text(
                          'Smart Recommendations',
                          style: MyStyles.title24White700.copyWith(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Top 5 recommendations based on your taste',
                        style: MyStyles.title24White400.copyWith(
                          fontSize: 14,
                          color: Colors.grey[400],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            vSpace(32),
            // Recommendations Grid
            _buildRecommendationsGrid(),
          ],
        ),
      ),
    );
  }

  // Movie Card Widget
  Widget _buildMovieCard(MovieModel movie, bool isSelected) {
    final canSelect = _selectedMovies.length < 5 || isSelected;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: GestureDetector(
        onTap: canSelect
            ? () {
                setState(() {
                  if (isSelected) {
                    _selectedMovies.remove(movie);
                  } else {
                    _selectedMovies.add(movie);
                  }
                });
              }
            : null,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: isSelected
                ? LinearGradient(
                    colors: [
                      Colors.blue.withOpacity(0.3),
                      Colors.purple.withOpacity(0.2),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
            border: Border.all(
              color: isSelected
                  ? Colors.blue
                  : canSelect
                      ? Colors.grey[700]!
                      : Colors.grey[800]!,
              width: isSelected ? 2.5 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: isSelected
                    ? Colors.blue.withOpacity(0.3)
                    : Colors.black.withOpacity(0.2),
                blurRadius: isSelected ? 15 : 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Stack(
              children: [
                // Movie Poster
                if (movie.posterPath != null)
                  Positioned.fill(
                    child: Image.network(
                      '$kmoviedbImageURL${movie.posterPath}',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildPlaceholderImage();
                      },
                    ),
                  )
                else
                  _buildPlaceholderImage(),

                // Overlay gradient
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                        stops: const [0.5, 1.0],
                      ),
                    ),
                  ),
                ),

                // Movie Title
                Positioned(
                  bottom: 8,
                  left: 8,
                  right: 8,
                  child: Text(
                    movie.title ?? 'Unknown',
                    style: MyStyles.title24White400.copyWith(
                      fontSize: 10, // Smaller font size
                      fontWeight: FontWeight.w700,
                      shadows: [
                        const Shadow(
                          color: Colors.black,
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // Selection indicator
                if (isSelected)
                  Positioned(
                    top: 6,
                    right: 6,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.all(4), // Smaller padding
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Colors.green, Colors.teal],
                        ),
                        borderRadius:
                            BorderRadius.circular(12), // Smaller radius
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.withOpacity(0.4),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.check_rounded,
                        color: Colors.white,
                        size: 14, // Smaller icon
                      ),
                    ),
                  ),

                // Rating badge
                if (movie.voteAverage != null)
                  Positioned(
                    top: 6,
                    left: 6,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6, // Smaller padding
                        vertical: 3,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.8),
                        borderRadius:
                            BorderRadius.circular(8), // Smaller radius
                        border: Border.all(
                          color: Colors.amber.withOpacity(0.5),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.star_rounded,
                            color: Colors.amber[400],
                            size: 10, // Smaller icon
                          ),
                          const SizedBox(width: 2),
                          Text(
                            movie.voteAverage!.toStringAsFixed(1),
                            style: MyStyles.title24White400.copyWith(
                              fontSize: 9, // Smaller font
                              fontWeight: FontWeight.w600,
                              color: Colors.amber[400],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // Disabled overlay
                if (!canSelect && !isSelected)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.block_rounded,
                              color: Colors.white70,
                              size: 20, // Smaller icon
                            ),
                            SizedBox(height: 4), // Smaller spacing
                            Text(
                              'Max 5',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 9, // Smaller font
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Placeholder Image Widget
  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey[800]!,
            Colors.grey[900]!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.movie_creation_outlined,
            color: Colors.grey[600],
            size: 40,
          ),
          const SizedBox(height: 12),
          Text(
            'No Image\nAvailable',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Empty Movie Slot Widget
  Widget _buildEmptyMovieSlot() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            MyColors.secondaryColor.withOpacity(0.8),
            MyColors.secondaryColor.withOpacity(0.6),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey[600]!.withOpacity(0.5),
          width: 2,
          style: BorderStyle.solid,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8), // Smaller padding
              decoration: BoxDecoration(
                color: Colors.grey[700]!.withOpacity(0.3),
                borderRadius: BorderRadius.circular(16), // Smaller radius
              ),
              child: Icon(
                Icons.add_rounded,
                color: Colors.grey[400],
                size: 24, // Smaller icon
              ),
            ),
            const SizedBox(height: 8), // Smaller spacing
            Text(
              'Add Movie',
              style: MyStyles.title24White400.copyWith(
                fontSize: 10, // Smaller font
                color: Colors.grey[400],
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              'Search above',
              style: MyStyles.title24White400.copyWith(
                fontSize: 8, // Smaller font
                color: Colors.grey[500],
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Search Results Widget
  Widget _buildSearchResults() {
    return BlocBuilder<SuggestSearchCubit, SuggestSearchState>(
      builder: (context, state) {
        if (state is SuggestSearchLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is SuggestSearchLoaded) {
          final movies = state.movies;
          if (movies.isEmpty) {
            return const Center(
              child: Text(
                'No movies found',
                style: TextStyle(color: Colors.grey),
              ),
            );
          }

          return SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: movies.length,
              itemBuilder: (context, index) {
                final movie = movies[index];
                final isSelected = _selectedMovies.contains(movie);
                return Container(
                  width: 140,
                  margin: const EdgeInsets.only(right: 12),
                  child: _buildMovieCard(movie, isSelected),
                );
              },
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  // Recommendations Grid
  Widget _buildRecommendationsGrid() {
    return FutureBuilder<List<MovieModel>>(
      future: _getRecommendations(_selectedMovies.toList()),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Text(
              'No recommendations available',
              style: TextStyle(color: Colors.grey),
            ),
          );
        }

        final recommendations = snapshot.data!;
        return SizedBox(
          height: 320, // Slightly increased height for better display
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            itemCount: recommendations.length,
            itemBuilder: (context, index) {
              final movie = recommendations[index];
              return Container(
                width: 180, // Slightly smaller width to fit 5 items better
                margin: EdgeInsets.only(
                  right: index < recommendations.length - 1 ? 16 : 0,
                ),
                child: _buildRecommendationCard(movie),
              );
            },
          ),
        );
      },
    );
  }

  // Recommendation Card
  Widget _buildRecommendationCard(MovieModel movie) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DetailsScreen(movie: movie),
          ),
        );
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Movie Poster
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                image: movie.posterPath != null
                    ? DecorationImage(
                        image: NetworkImage(
                          '$kmoviedbImageURL${movie.posterPath}',
                        ),
                        fit: BoxFit.cover,
                      )
                    : null,
                color:
                    movie.posterPath == null ? MyColors.secondaryColor : null,
              ),
              child: Stack(
                children: [
                  // Rating Badge
                  if (movie.voteAverage != null)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 12,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              movie.voteAverage!.toStringAsFixed(1),
                              style: MyStyles.title24White400.copyWith(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          vSpace(8),
          // Movie Info
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Country and Year
              Text(
                'USA, ${movie.releaseDate?.substring(0, 4) ?? '2024'}',
                style: MyStyles.title24White400.copyWith(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
              vSpace(4),
              // Title
              Text(
                movie.title ?? 'Unknown',
                style: MyStyles.title24White400.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              vSpace(4),
              // Genre - عرض التصنيفات الحقيقية
              Text(
                _getMovieGenres(movie),
                style: MyStyles.title24White400.copyWith(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // دالة لاستخراج أسماء التصنيفات من معرفات التصنيفات
  String _getMovieGenres(MovieModel movie) {
    if (movie.genreIds == null || movie.genreIds!.isEmpty) {
      return 'Unknown';
    }

    // خريطة معرفات التصنيفات إلى أسمائها
    const genreMap = {
      28: 'Action',
      12: 'Adventure',
      16: 'Animation',
      35: 'Comedy',
      80: 'Crime',
      99: 'Documentary',
      18: 'Drama',
      10751: 'Family',
      14: 'Fantasy',
      36: 'History',
      27: 'Horror',
      10402: 'Music',
      9648: 'Mystery',
      10749: 'Romance',
      878: 'Sci-Fi',
      10770: 'TV Movie',
      53: 'Thriller',
      10752: 'War',
      37: 'Western',
    };

    // تحويل معرفات التصنيفات إلى أسماء وأخذ أول 2-3 تصنيفات فقط
    final genreNames = movie.genreIds!
        .map((id) => genreMap[id])
        .where((name) => name != null)
        .take(3)
        .join(', ');

    return genreNames.isNotEmpty ? genreNames : 'Unknown';
  }

  // Helper method to get smart recommendations
  Future<List<MovieModel>> _getRecommendations(
      List<MovieModel> selectedMovies) async {
    if (selectedMovies.isEmpty) {
      return [];
    }

    try {
      final apiService = ApiService();
      final Set<MovieModel> allCandidates = {};

      // تحليل الأفلام المختارة لفهم تفضيلات المستخدم
      final userPreferences = _analyzeUserPreferences(selectedMovies);

      print('🎯 تحليل تفضيلات المستخدم:');
      print(
          '📽️ الأفلام المختارة: ${selectedMovies.map((m) => m.title).join(", ")}');
      print('🎭 الأنواع المفضلة: ${userPreferences['preferredGenres']}');
      print(
          '⭐ متوسط التقييم: ${userPreferences['averageRating'].toStringAsFixed(1)}');
      print(
          '📅 نطاق السنوات: ${userPreferences['yearRange']['min']}-${userPreferences['yearRange']['max']}');
      print(
          '🎬 الممثلين المفضلين: ${(userPreferences['preferredActors'] as List<String>).take(5).join(", ")}');

      // الحصول على أفلام مشابهة لكل فيلم مختار
      for (final movie in selectedMovies) {
        if (movie.id != null) {
          try {
            print('🔍 البحث عن أفلام مشابهة لـ: ${movie.title}');
            final similarMovies = await apiService.getMovieData(
              MovieType.similar,
              movieID: movie.id!,
            );
            allCandidates.addAll(similarMovies);
            print('✅ تم العثور على ${similarMovies.length} فيلم مشابه');
          } catch (e) {
            print('❌ خطأ في الحصول على أفلام مشابهة لـ ${movie.title}: $e');
          }
        }
      }

      // إضافة أفلام من مصادر متعددة لضمان التنوع
      await _addMoviesFromMultipleSources(
          apiService, allCandidates, userPreferences);

      // إزالة الأفلام المختارة والمكررة
      final selectedIds = selectedMovies.map((m) => m.id).toSet();
      final uniqueCandidates = <int, MovieModel>{};

      // إزالة المكررات باستخدام ID كمفتاح
      for (final movie in allCandidates) {
        if (movie.id != null && !selectedIds.contains(movie.id)) {
          uniqueCandidates[movie.id!] = movie;
        }
      }

      final filteredCandidates = uniqueCandidates.values.toList();

      print('🎬 إجمالي الأفلام المرشحة: ${filteredCandidates.length}');

      // ضمان الحصول على 5 توصيات بالضبط
      final finalRecommendations = await _ensureExactly5Recommendations(
        filteredCandidates,
        selectedMovies,
        userPreferences,
        apiService,
      );

      print(
          '🌟 التوصيات النهائية (${finalRecommendations.length}): ${finalRecommendations.map((m) => m.title).join(", ")}');

      return finalRecommendations;
    } catch (e) {
      print('❌ خطأ في الحصول على التوصيات: $e');
      return [];
    }
  }

  // تحليل تفضيلات المستخدم
  Map<String, dynamic> _analyzeUserPreferences(
      List<MovieModel> selectedMovies) {
    // تحليل الأنواع المفضلة
    final Map<int, int> genreCount = {};
    final List<double> ratings = [];
    final List<int> years = [];
    final Set<String> keywords = {};

    for (final movie in selectedMovies) {
      // تحليل النوعية
      if (movie.genreIds != null) {
        for (final genreId in movie.genreIds!) {
          genreCount[genreId] = (genreCount[genreId] ?? 0) + 1;
        }
      }

      // تحليل التقييمات
      if (movie.voteAverage != null && movie.voteAverage! > 0) {
        ratings.add(movie.voteAverage!);
      }

      // تحليل السنوات
      if (movie.releaseDate != null && movie.releaseDate!.isNotEmpty) {
        try {
          final year = int.parse(movie.releaseDate!.substring(0, 4));
          years.add(year);
        } catch (e) {
          // تجاهل الأخطاء في تحليل التاريخ
        }
      }

      // تحليل الكلمات المفتاحية من العنوان
      if (movie.title != null) {
        final titleWords = movie.title!.toLowerCase().split(' ');
        for (final word in titleWords) {
          if (word.length > 3) {
            // تجاهل الكلمات القصيرة
            keywords.add(word);
          }
        }
      }
    }

    // حساب الأنواع المفضلة
    final preferredGenres = genreCount.entries
        .where((entry) =>
            entry.value >= 2) // النوعية التي تظهر في فيلمين على الأقل
        .map((entry) => entry.key)
        .toList();

    // حساب متوسط التقييم
    final averageRating = ratings.isNotEmpty
        ? ratings.reduce((a, b) => a + b) / ratings.length
        : 7.0;

    // حساب نطاق السنوات
    final minYear =
        years.isNotEmpty ? years.reduce((a, b) => a < b ? a : b) : 2020;
    final maxYear =
        years.isNotEmpty ? years.reduce((a, b) => a > b ? a : b) : 2024;

    // تحليل الممثلين المفضلين (محاكاة بناءً على خصائص الأفلام)
    final preferredActors = _analyzePreferredActors(selectedMovies);

    return {
      'preferredGenres': preferredGenres,
      'averageRating': averageRating,
      'ratingRange': [averageRating - 1.5, averageRating + 1.5],
      'yearRange': {'min': minYear - 5, 'max': maxYear + 2},
      'keywords': keywords.toList(),
      'preferredActors': preferredActors,
    };
  }

  // تحليل الممثلين المفضلين بناءً على خصائص الأفلام المختارة
  List<String> _analyzePreferredActors(List<MovieModel> selectedMovies) {
    final List<String> preferredActors = [];

    // قوائم الممثلين حسب النوعية والخصائص
    final Map<String, List<String>> genreActors = {
      'action': [
        'Tom Cruise',
        'Dwayne Johnson',
        'Jason Statham',
        'Vin Diesel',
        'Chris Evans',
        'Chris Hemsworth',
        'Robert Downey Jr.',
        'Will Smith',
        'Keanu Reeves',
        'Tom Hardy',
        'Mark Wahlberg',
        'Liam Neeson'
      ],
      'comedy': [
        'Adam Sandler',
        'Kevin Hart',
        'Ryan Reynolds',
        'Chris Pratt',
        'Steve Carell',
        'Jim Carrey',
        'Ben Stiller',
        'Owen Wilson',
        'Vince Vaughn',
        'Paul Rudd',
        'Seth Rogen',
        'Jonah Hill'
      ],
      'drama': [
        'Leonardo DiCaprio',
        'Brad Pitt',
        'Matthew McConaughey',
        'Christian Bale',
        'Denzel Washington',
        'Morgan Freeman',
        'Anthony Hopkins',
        'Gary Oldman',
        'Joaquin Phoenix',
        'Oscar Isaac',
        'Michael Fassbender',
        'Jake Gyllenhaal'
      ],
      'romance': [
        'Ryan Gosling',
        'Hugh Jackman',
        'Bradley Cooper',
        'Ryan Reynolds',
        'Channing Tatum',
        'Nicholas Sparks',
        'Matthew McConaughey',
        'Patrick Swayze'
      ],
      'thriller': [
        'Liam Neeson',
        'Denzel Washington',
        'Jake Gyllenhaal',
        'Christian Bale',
        'Tom Hardy',
        'Oscar Isaac',
        'Michael Fassbender',
        'Benedict Cumberbatch'
      ],
      'sci-fi': [
        'Keanu Reeves',
        'Tom Cruise',
        'Matt Damon',
        'Christian Bale',
        'Ryan Gosling',
        'Oscar Isaac',
        'Michael Fassbender',
        'Benedict Cumberbatch'
      ],
      'animation': [
        'Tom Hanks',
        'Chris Pratt',
        'Mike Myers',
        'Eddie Murphy',
        'Will Smith',
        'Robin Williams',
        'Jack Black',
        'Ben Stiller'
      ],
      'horror': [
        'Patrick Wilson',
        'Vera Farmiga',
        'James Wan',
        'Ethan Hawke',
        'Toni Collette',
        'Lupita Nyong\'o',
        'John Krasinski',
        'Emily Blunt'
      ]
    };

    // تحليل الأنواع في الأفلام المختارة
    final Map<int, String> genreMap = {
      28: 'action',
      12: 'action', // Adventure مشابه للـ Action
      16: 'animation',
      35: 'comedy',
      18: 'drama',
      14: 'sci-fi', // Fantasy مشابه للـ Sci-Fi
      27: 'horror',
      10749: 'romance',
      878: 'sci-fi',
      53: 'thriller',
    };

    final Set<String> detectedGenres = {};

    // استخراج الأنواع من الأفلام المختارة
    for (final movie in selectedMovies) {
      if (movie.genreIds != null) {
        for (final genreId in movie.genreIds!) {
          final genreType = genreMap[genreId];
          if (genreType != null) {
            detectedGenres.add(genreType);
          }
        }
      }
    }

    // إضافة ممثلين بناءً على الأنواع المكتشفة
    for (final genre in detectedGenres) {
      final actors = genreActors[genre];
      if (actors != null) {
        // أخذ أول 3-4 ممثلين من كل نوعية
        preferredActors.addAll(actors.take(4));
      }
    }

    // إضافة ممثلين مشهورين عامين إذا لم نجد أنواع محددة
    if (preferredActors.isEmpty) {
      preferredActors.addAll([
        'Leonardo DiCaprio',
        'Tom Hanks',
        'Brad Pitt',
        'Will Smith',
        'Robert Downey Jr.',
        'Chris Evans',
        'Ryan Reynolds',
        'Dwayne Johnson'
      ]);
    }

    // إزالة المكررات والحد من العدد
    return preferredActors.toSet().take(15).toList();
  }

  // إضافة أفلام من نفس النوعية
  Future<void> _addMoviesByGenre(
    ApiService apiService,
    Set<MovieModel> allCandidates,
    Map<String, dynamic> userPreferences,
  ) async {
    final preferredGenres = userPreferences['preferredGenres'] as List<int>;

    if (preferredGenres.isNotEmpty) {
      try {
        // الحصول على أفلام شائعة من نفس النوعية
        final popularMovies = await apiService.getMovieData(MovieType.popular);
        final topRatedMovies =
            await apiService.getMovieData(MovieType.topRated);

        // تصفية الأفلام حسب النوعية المفضلة
        final genreFilteredMovies =
            [...popularMovies, ...topRatedMovies].where((movie) {
          if (movie.genreIds == null) return false;
          return movie.genreIds!
              .any((genreId) => preferredGenres.contains(genreId));
        }).toList();

        allCandidates.addAll(genreFilteredMovies);
        print(
            '🎭 تم إضافة ${genreFilteredMovies.length} فيلم من الأنواع المفضلة');
      } catch (e) {
        print('❌ خطأ في إضافة أفلام من نفس النوعية: $e');
      }
    }
  }

  // تطبيق خوارزمية التوصية الذكية
  List<MovieModel> _applySmartRecommendationAlgorithm(
    List<MovieModel> candidates,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
  ) {
    final List<MapEntry<MovieModel, double>> scoredMovies = [];
    final Set<int> processedIds = {};

    // حساب النقاط مع تجنب المكررات
    for (final movie in candidates) {
      if (movie.id != null && !processedIds.contains(movie.id)) {
        final score =
            _calculateMovieScore(movie, selectedMovies, userPreferences);
        scoredMovies.add(MapEntry(movie, score));
        processedIds.add(movie.id!);
      }
    }

    // ترتيب الأفلام حسب النقاط (من الأعلى للأقل)
    scoredMovies.sort((a, b) => b.value.compareTo(a.value));

    // فلترة إضافية لضمان التنوع
    final diverseRecommendations =
        _ensureDiverseRecommendations(scoredMovies, userPreferences);

    // طباعة أفضل 10 نتائج للتحليل
    print('🏆 أفضل 10 أفلام مرشحة (بعد ضمان التنوع):');
    for (int i = 0; i < 10 && i < diverseRecommendations.length; i++) {
      final entry = diverseRecommendations[i];
      print(
          '${i + 1}. ${entry.key.title} - النقاط: ${entry.value.toStringAsFixed(2)}');
    }

    return diverseRecommendations.map((entry) => entry.key).toList();
  }

  // ضمان التنوع في التوصيات مع تركيز على النوعية
  List<MapEntry<MovieModel, double>> _ensureDiverseRecommendations(
    List<MapEntry<MovieModel, double>> scoredMovies,
    Map<String, dynamic> userPreferences,
  ) {
    final List<MapEntry<MovieModel, double>> diverseList = [];
    final Set<String> usedTitles = {};
    final Map<int, int> genreCount = {};
    final Set<int> usedYears = {};
    final preferredGenres = userPreferences['preferredGenres'] as List<int>;

    // مرحلة 1: إعطاء أولوية للأفلام من النوعيات المفضلة
    final preferredGenreMovies = <MapEntry<MovieModel, double>>[];
    final otherMovies = <MapEntry<MovieModel, double>>[];

    for (final entry in scoredMovies) {
      final movie = entry.key;
      bool hasPreferredGenre = false;

      if (movie.genreIds != null && preferredGenres.isNotEmpty) {
        hasPreferredGenre =
            movie.genreIds!.any((id) => preferredGenres.contains(id));
      }

      if (hasPreferredGenre) {
        preferredGenreMovies.add(entry);
      } else {
        otherMovies.add(entry);
      }
    }

    // مرحلة 2: معالجة الأفلام من النوعيات المفضلة أولاً
    final combinedList = [...preferredGenreMovies, ...otherMovies];

    for (final entry in combinedList) {
      final movie = entry.key;
      bool shouldAdd = true;

      // تجنب الأفلام بنفس الاسم (أو أسماء متشابهة جداً)
      if (movie.title != null) {
        final normalizedTitle = _normalizeTitle(movie.title!);
        if (usedTitles.contains(normalizedTitle)) {
          shouldAdd = false;
        } else {
          usedTitles.add(normalizedTitle);
        }
      }

      // ضمان التنوع في الأنواع مع مرونة أكبر للنوعيات المفضلة
      if (shouldAdd && movie.genreIds != null) {
        bool genreOverused = false;
        final hasPreferredGenre =
            movie.genreIds!.any((id) => preferredGenres.contains(id));

        for (final genreId in movie.genreIds!) {
          final currentCount = genreCount[genreId] ?? 0;
          // مرونة أكبر للنوعيات المفضلة (4 أفلام بدلاً من 3)
          final maxAllowed = hasPreferredGenre ? 4 : 2;

          if (currentCount >= maxAllowed) {
            genreOverused = true;
            break;
          }
        }

        if (genreOverused) {
          shouldAdd = false;
        } else {
          // تحديث عداد الأنواع
          for (final genreId in movie.genreIds!) {
            genreCount[genreId] = (genreCount[genreId] ?? 0) + 1;
          }
        }
      }

      // ضمان التنوع في السنوات (لا أكثر من فيلمين من نفس السنة)
      if (shouldAdd &&
          movie.releaseDate != null &&
          movie.releaseDate!.isNotEmpty) {
        try {
          final year = int.parse(movie.releaseDate!.substring(0, 4));
          if (usedYears.contains(year) && diverseList.length >= 2) {
            // السماح بفيلمين فقط من نفس السنة
            final sameYearCount = diverseList.where((item) {
              if (item.key.releaseDate == null ||
                  item.key.releaseDate!.isEmpty) {
                return false;
              }
              try {
                final itemYear =
                    int.parse(item.key.releaseDate!.substring(0, 4));
                return itemYear == year;
              } catch (e) {
                return false;
              }
            }).length;

            if (sameYearCount >= 2) {
              shouldAdd = false;
            }
          }
          if (shouldAdd) {
            usedYears.add(year);
          }
        } catch (e) {
          // تجاهل أخطاء تحليل التاريخ
        }
      }

      if (shouldAdd) {
        diverseList.add(entry);

        // التوقف عند الوصول لـ 10 توصيات متنوعة
        if (diverseList.length >= 10) {
          break;
        }
      }
    }

    return diverseList;
  }

  // تطبيع عنوان الفيلم لمقارنة أفضل
  String _normalizeTitle(String title) {
    return title
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '') // إزالة علامات الترقيم
        .replaceAll(RegExp(r'\s+'), ' ') // توحيد المسافات
        .trim();
  }

  // حساب نقاط الفيلم بناءً على التشابه المحسن
  double _calculateMovieScore(
    MovieModel movie,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
  ) {
    double score = 0.0;

    // 1. نقاط التشابه في الاسم/الكلمات المفتاحية (30% من النقاط) - أولوية عالية
    score +=
        _calculateAdvancedTitleScore(movie, selectedMovies, userPreferences) *
            0.30;

    // 2. نقاط التشابه في النوعية (20% من النقاط)
    score +=
        _calculateAdvancedGenreScore(movie, selectedMovies, userPreferences) *
            0.20;

    // 3. نقاط التشابه في الممثلين (20% من النقاط) - جديد ومهم!
    score +=
        _calculateActorSimilarityScore(movie, selectedMovies, userPreferences) *
            0.20;

    // 4. نقاط التشابه المباشر مع الأفلام المختارة (15% من النقاط)
    score += _calculateDirectSimilarityScore(movie, selectedMovies) * 0.15;

    // 5. نقاط التشابه في التقييم (8% من النقاط)
    score += _calculateAdvancedRatingScore(movie, userPreferences) * 0.08;

    // 6. نقاط التشابه في السنة (5% من النقاط)
    score += _calculateAdvancedYearScore(movie, userPreferences) * 0.05;

    // 7. نقاط الشعبية والجودة (2% من النقاط)
    score += _calculateAdvancedPopularityScore(movie) * 0.02;

    return score;
  }

  // حساب نقاط التشابه في الممثلين - جديد ومحسن!
  double _calculateActorSimilarityScore(
    MovieModel movie,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
  ) {
    // إذا لم تكن هناك أفلام مختارة، نعطي نقاط متوسطة
    if (selectedMovies.isEmpty) return 50.0;

    // الحصول على قائمة الممثلين المفضلين من تفضيلات المستخدم
    final preferredActors =
        userPreferences['preferredActors'] as List<String>? ?? [];

    double totalScore = 0.0;
    int comparisons = 0;

    // مقارنة مع كل فيلم مختار
    for (final selectedMovie in selectedMovies) {
      final actorScore =
          _calculateActorMatchScore(movie, selectedMovie, preferredActors);
      totalScore += actorScore;
      comparisons++;
    }

    // حساب المتوسط مع إضافة نقاط إضافية للممثلين المشهورين
    double averageScore = comparisons > 0 ? totalScore / comparisons : 0.0;

    // إضافة نقاط للممثلين المشهورين والمحبوبين
    averageScore += _calculatePopularActorBonus(movie);

    return averageScore.clamp(0.0, 100.0);
  }

  // حساب نقاط التطابق في الممثلين بين فيلمين
  double _calculateActorMatchScore(
    MovieModel movie1,
    MovieModel movie2,
    List<String> preferredActors,
  ) {
    // قائمة الممثلين المشهورين والمحبوبين (يمكن توسيعها)
    final popularActors = [
      'Tom Hanks',
      'Leonardo DiCaprio',
      'Brad Pitt',
      'Will Smith',
      'Johnny Depp',
      'Robert Downey Jr.',
      'Chris Evans',
      'Chris Hemsworth',
      'Scarlett Johansson',
      'Jennifer Lawrence',
      'Emma Stone',
      'Ryan Gosling',
      'Matthew McConaughey',
      'Christian Bale',
      'Morgan Freeman',
      'Samuel L. Jackson',
      'Denzel Washington',
      'Tom Cruise',
      'Harrison Ford',
      'Matt Damon',
      'Mark Wahlberg',
      'Dwayne Johnson',
      'Ryan Reynolds',
      'Chris Pratt',
      'Benedict Cumberbatch',
      'Tom Holland',
      'Anthony Hopkins',
      'Gary Oldman',
      'Hugh Jackman',
      'Jake Gyllenhaal',
      'Oscar Isaac',
      'Michael Fassbender',
      'James McAvoy',
      'Patrick Stewart',
      'Ian McKellen',
      'Meryl Streep',
      'Cate Blanchett',
      'Natalie Portman',
      'Amy Adams',
      'Jessica Chastain',
      'Charlize Theron',
      'Margot Robbie',
      'Gal Gadot',
      'Brie Larson',
      'Lupita Nyong\'o',
      'Viola Davis',
      'Mahershala Ali',
      'Michael B. Jordan',
      'Chadwick Boseman',
      'Idris Elba',
      'John Boyega',
      'Adam Driver',
      'Timothée Chalamet',
      'Saoirse Ronan',
      'Florence Pugh',
      'Anya Taylor-Joy',
      'Zendaya',
      'Tom Hardy',
      'Eddie Redmayne',
      'Rami Malek',
      'Joaquin Phoenix',
      'Adam Sandler',
      'Kevin Hart',
      'The Rock',
      'Vin Diesel',
      'Jason Statham',
      'Keanu Reeves'
    ];

    double score = 0.0;

    // نقاط للممثلين المشتركين (محاكاة - في التطبيق الحقيقي نحتاج API للممثلين)
    // هنا نستخدم خوارزمية تقديرية بناءً على النوعية والشعبية

    // إذا كان الفيلمان من نفس النوعية، احتمالية وجود ممثلين مشتركين أعلى
    if (movie1.genreIds != null && movie2.genreIds != null) {
      final commonGenres = movie1.genreIds!
          .where((genre) => movie2.genreIds!.contains(genre))
          .length;

      if (commonGenres > 0) {
        score += commonGenres * 15.0; // نقاط للأنواع المشتركة
      }
    }

    // نقاط إضافية للأفلام من نفس الفترة الزمنية (نفس الممثلين النشطين)
    final year1 = int.tryParse(movie1.year) ?? 0;
    final year2 = int.tryParse(movie2.year) ?? 0;

    if (year1 > 0 && year2 > 0) {
      final yearDifference = (year1 - year2).abs();
      if (yearDifference <= 3) {
        score += 20.0; // نفس الفترة الزمنية
      } else if (yearDifference <= 7) {
        score += 10.0; // فترة زمنية قريبة
      }
    }

    // نقاط للأفلام عالية التقييم (ممثلين مشهورين)
    final rating1 = movie1.voteAverage ?? 0.0;
    final rating2 = movie2.voteAverage ?? 0.0;

    if (rating1 >= 7.5 && rating2 >= 7.5) {
      score += 15.0; // أفلام عالية الجودة
    }

    return score.clamp(0.0, 100.0);
  }

  // حساب نقاط إضافية للممثلين المشهورين
  double _calculatePopularActorBonus(MovieModel movie) {
    double bonus = 0.0;

    // نقاط إضافية للأفلام عالية الشعبية (تحتوي على ممثلين مشهورين)
    if (movie.popularity != null && movie.popularity! > 50.0) {
      bonus += 10.0;
    }

    // نقاط إضافية للأفلام عالية التقييم
    if (movie.voteAverage != null && movie.voteAverage! >= 7.5) {
      bonus += 15.0;
    }

    // نقاط إضافية للأفلام الحديثة (ممثلين نشطين حالياً)
    final year = int.tryParse(movie.year) ?? 0;
    final currentYear = DateTime.now().year;

    if (year >= currentYear - 5) {
      bonus += 10.0; // أفلام حديثة
    } else if (year >= currentYear - 10) {
      bonus += 5.0; // أفلام حديثة نسبياً
    }

    return bonus;
  }

  // دالة مساعدة لجلب بيانات الممثلين من API (للاستخدام المستقبلي)
  Future<List<String>> _getMovieActors(int movieId) async {
    try {
      final apiService = ApiService();
      final cast = await apiService.getCastlist(movieId, ProgramType.movie);

      // استخراج أسماء أهم 5 ممثلين
      return cast
          .take(5)
          .map((actor) => actor.displayName)
          .where((name) => name.isNotEmpty)
          .toList();
    } catch (e) {
      print('❌ خطأ في جلب بيانات الممثلين للفيلم $movieId: $e');
      return [];
    }
  }

  // تحسين نقاط التوصية بناءً على الممثلين الحقيقيين (للمستقبل)
  Future<double> _calculateRealActorSimilarity(
    MovieModel movie1,
    MovieModel movie2,
  ) async {
    if (movie1.id == null || movie2.id == null) return 0.0;

    try {
      final actors1 = await _getMovieActors(movie1.id!);
      final actors2 = await _getMovieActors(movie2.id!);

      if (actors1.isEmpty || actors2.isEmpty) return 0.0;

      // حساب عدد الممثلين المشتركين
      final commonActors =
          actors1.where((actor) => actors2.contains(actor)).length;

      // حساب النسبة المئوية للتشابه
      final totalActors = (actors1.length + actors2.length) / 2;
      return (commonActors / totalActors) * 100;
    } catch (e) {
      print('❌ خطأ في حساب تشابه الممثلين: $e');
      return 0.0;
    }
  }

  // حساب نقاط التشابه في النوعية
  double _calculateGenreScore(
      MovieModel movie, Map<String, dynamic> userPreferences) {
    final preferredGenres = userPreferences['preferredGenres'] as List<int>;
    if (preferredGenres.isEmpty || movie.genreIds == null) return 0.0;

    final matchingGenres = movie.genreIds!
        .where((genreId) => preferredGenres.contains(genreId))
        .length;

    // نقاط أعلى للأفلام التي تشارك أكثر من نوعية
    return (matchingGenres / preferredGenres.length) * 100;
  }

  // حساب نقاط التشابه في التقييم
  double _calculateRatingScore(
      MovieModel movie, Map<String, dynamic> userPreferences) {
    if (movie.voteAverage == null) return 0.0;

    final averageRating = userPreferences['averageRating'] as double;
    final ratingDifference = (movie.voteAverage! - averageRating).abs();

    // نقاط أعلى للأفلام القريبة من متوسط التقييم المفضل
    if (ratingDifference <= 0.5) return 100.0;
    if (ratingDifference <= 1.0) return 80.0;
    if (ratingDifference <= 1.5) return 60.0;
    if (ratingDifference <= 2.0) return 40.0;
    return 20.0;
  }

  // حساب نقاط التشابه في السنة
  double _calculateYearScore(
      MovieModel movie, Map<String, dynamic> userPreferences) {
    if (movie.releaseDate == null || movie.releaseDate!.isEmpty) return 50.0;

    try {
      final movieYear = int.parse(movie.releaseDate!.substring(0, 4));
      final yearRange = userPreferences['yearRange'] as Map<String, dynamic>;
      final minYear = yearRange['min'] as int;
      final maxYear = yearRange['max'] as int;

      if (movieYear >= minYear && movieYear <= maxYear) {
        return 100.0; // في النطاق المفضل
      } else {
        // نقاط أقل كلما ابتعد عن النطاق
        final distance =
            movieYear < minYear ? minYear - movieYear : movieYear - maxYear;
        return (100.0 - (distance * 5)).clamp(0.0, 100.0);
      }
    } catch (e) {
      return 50.0; // نقاط متوسطة في حالة الخطأ
    }
  }

  // حساب نقاط التشابه في الاسم
  double _calculateTitleScore(
      MovieModel movie, Map<String, dynamic> userPreferences) {
    if (movie.title == null) return 0.0;

    final keywords = userPreferences['keywords'] as List<String>;
    if (keywords.isEmpty) return 50.0;

    final movieTitle = movie.title!.toLowerCase();
    int matchingKeywords = 0;

    for (final keyword in keywords) {
      if (movieTitle.contains(keyword)) {
        matchingKeywords++;
      }
    }

    return (matchingKeywords / keywords.length) * 100;
  }

  // حساب نقاط الشعبية والجودة
  double _calculatePopularityScore(MovieModel movie) {
    double score = 0.0;

    // نقاط التقييم
    if (movie.voteAverage != null) {
      score += (movie.voteAverage! / 10) * 50; // حتى 50 نقطة
    }

    // نقاط الشعبية
    if (movie.popularity != null) {
      // تطبيع الشعبية (عادة تكون بين 0-100)
      final normalizedPopularity = (movie.popularity! / 100).clamp(0.0, 1.0);
      score += normalizedPopularity * 50; // حتى 50 نقطة
    }

    return score;
  }

  // ===== الدوال المحسنة للتوصيات =====

  // حساب نقاط النوعية المحسن والمطور
  double _calculateAdvancedGenreScore(
    MovieModel movie,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
  ) {
    if (movie.genreIds == null || movie.genreIds!.isEmpty) return 0.0;

    final preferredGenres = userPreferences['preferredGenres'] as List<int>;
    if (preferredGenres.isEmpty) return 30.0;

    double score = 0.0;

    // 1. نقاط التطابق المباشر في النوعية (40% من نقاط النوعية)
    final matchingGenres = movie.genreIds!
        .where((genreId) => preferredGenres.contains(genreId))
        .length;

    if (matchingGenres > 0) {
      // نقاط إضافية للأفلام التي تشارك أكثر من نوعية
      final genreMatchRatio = matchingGenres / movie.genreIds!.length;
      final preferenceMatchRatio = matchingGenres / preferredGenres.length;

      // دمج النسبتين مع وزن أكبر لتطابق التفضيلات
      score += ((preferenceMatchRatio * 0.7) + (genreMatchRatio * 0.3)) * 40;
    }

    // 2. تحليل تكرار الأنواع في الأفلام المختارة (35% من نقاط النوعية)
    final Map<int, double> genreImportanceScore = {};
    for (final selectedMovie in selectedMovies) {
      if (selectedMovie.genreIds != null) {
        for (final genreId in selectedMovie.genreIds!) {
          genreImportanceScore[genreId] =
              (genreImportanceScore[genreId] ?? 0.0) +
                  (1.0 / selectedMovie.genreIds!.length);
        }
      }
    }

    double genreFrequencyScore = 0.0;
    for (final genreId in movie.genreIds!) {
      final importance = genreImportanceScore[genreId] ?? 0.0;
      if (importance > 0) {
        // نقاط أعلى للأنواع الأكثر تكراراً وأهمية
        genreFrequencyScore += (importance / selectedMovies.length) * 100;
      }
    }
    score += (genreFrequencyScore / movie.genreIds!.length) * 35;

    // 3. تحليل الأنواع المتوافقة والمكملة (15% من نقاط النوعية)
    score += _calculateCompatibleGenresScore(movie, selectedMovies) * 15;

    // 4. نقاط النوعية الرئيسية مقابل الثانوية (10% من نقاط النوعية)
    score += _calculatePrimaryGenreScore(movie, preferredGenres) * 10;

    return score.clamp(0.0, 100.0);
  }

  // حساب نقاط الأنواع المتوافقة والمكملة
  double _calculateCompatibleGenresScore(
      MovieModel movie, List<MovieModel> selectedMovies) {
    if (movie.genreIds == null || selectedMovies.isEmpty) return 0.0;

    // خريطة الأنواع المتوافقة (الأنواع التي تظهر معاً بكثرة) - محدثة ودقيقة
    final Map<int, List<int>> compatibleGenres = {
      28: [
        12,
        53,
        878,
        80,
        10752
      ], // Action -> Adventure, Thriller, Sci-Fi, Crime, War
      12: [
        28,
        14,
        16,
        10751
      ], // Adventure -> Action, Fantasy, Animation, Family
      16: [
        10751,
        35,
        14,
        12
      ], // Animation -> Family, Comedy, Fantasy, Adventure
      35: [10749, 18, 10751, 16], // Comedy -> Romance, Drama, Family, Animation
      80: [53, 18, 9648, 28], // Crime -> Thriller, Drama, Mystery, Action
      99: [36, 18, 10752], // Documentary -> History, Drama, War
      18: [
        10749,
        80,
        36,
        35,
        10752
      ], // Drama -> Romance, Crime, History, Comedy, War
      10751: [
        16,
        35,
        14,
        12
      ], // Family -> Animation, Comedy, Fantasy, Adventure
      14: [
        12,
        28,
        16,
        10751
      ], // Fantasy -> Adventure, Action, Animation, Family
      36: [18, 10752, 99, 28], // History -> Drama, War, Documentary, Action
      27: [53, 9648, 80], // Horror -> Thriller, Mystery, Crime
      10402: [18, 10749, 35], // Music -> Drama, Romance, Comedy
      9648: [53, 80, 27, 18], // Mystery -> Thriller, Crime, Horror, Drama
      10749: [35, 18, 10402, 10751], // Romance -> Comedy, Drama, Music, Family
      878: [
        28,
        53,
        12,
        14
      ], // Science Fiction -> Action, Thriller, Adventure, Fantasy
      10770: [18, 35, 10749], // TV Movie -> Drama, Comedy, Romance
      53: [
        28,
        80,
        9648,
        27,
        878
      ], // Thriller -> Action, Crime, Mystery, Horror, Sci-Fi
      10752: [18, 28, 36, 99], // War -> Drama, Action, History, Documentary
      37: [28, 18, 12] // Western -> Action, Drama, Adventure
    };

    double compatibilityScore = 0.0;
    int totalComparisons = 0;

    for (final selectedMovie in selectedMovies) {
      if (selectedMovie.genreIds != null) {
        for (final selectedGenreId in selectedMovie.genreIds!) {
          for (final movieGenreId in movie.genreIds!) {
            // تحقق من التوافق المباشر
            if (selectedGenreId == movieGenreId) {
              compatibilityScore += 100.0;
            } else if (compatibleGenres[selectedGenreId]
                    ?.contains(movieGenreId) ==
                true) {
              compatibilityScore += 60.0;
            } else if (compatibleGenres[movieGenreId]
                    ?.contains(selectedGenreId) ==
                true) {
              compatibilityScore += 60.0;
            }
            totalComparisons++;
          }
        }
      }
    }

    return totalComparisons > 0 ? compatibilityScore / totalComparisons : 0.0;
  }

  // حساب نقاط النوعية الرئيسية
  double _calculatePrimaryGenreScore(
      MovieModel movie, List<int> preferredGenres) {
    if (movie.genreIds == null ||
        movie.genreIds!.isEmpty ||
        preferredGenres.isEmpty) {
      return 0.0;
    }

    // النوعية الأولى عادة هي الأهم
    final primaryGenre = movie.genreIds!.first;

    if (preferredGenres.contains(primaryGenre)) {
      // نقاط إضافية إذا كانت النوعية الرئيسية مطابقة للتفضيلات
      return 100.0;
    } else {
      // نقاط أقل إذا كانت النوعية الرئيسية غير مطابقة
      final hasAnyPreferredGenre =
          movie.genreIds!.any((id) => preferredGenres.contains(id));
      return hasAnyPreferredGenre ? 50.0 : 0.0;
    }
  }

  // حساب نقاط التقييم المحسن
  double _calculateAdvancedRatingScore(
    MovieModel movie,
    Map<String, dynamic> userPreferences,
  ) {
    if (movie.voteAverage == null) return 30.0;

    final averageRating = userPreferences['averageRating'] as double;
    final ratingDifference = (movie.voteAverage! - averageRating).abs();

    // منحنى تقييم أكثر سلاسة
    if (ratingDifference <= 0.3) return 100.0;
    if (ratingDifference <= 0.6) return 95.0;
    if (ratingDifference <= 1.0) return 85.0;
    if (ratingDifference <= 1.5) return 70.0;
    if (ratingDifference <= 2.0) return 50.0;
    if (ratingDifference <= 2.5) return 30.0;
    return 10.0;
  }

  // حساب نقاط السنة المحسن
  double _calculateAdvancedYearScore(
    MovieModel movie,
    Map<String, dynamic> userPreferences,
  ) {
    if (movie.releaseDate == null || movie.releaseDate!.isEmpty) return 40.0;

    try {
      final movieYear = int.parse(movie.releaseDate!.substring(0, 4));
      final yearRange = userPreferences['yearRange'] as Map<String, dynamic>;
      final minYear = yearRange['min'] as int;
      final maxYear = yearRange['max'] as int;
      final centerYear = (minYear + maxYear) / 2;

      // نقاط أعلى للأفلام القريبة من مركز النطاق المفضل
      if (movieYear >= minYear && movieYear <= maxYear) {
        final distanceFromCenter = (movieYear - centerYear).abs();
        final maxDistance = (maxYear - minYear) / 2;
        return 100.0 - (distanceFromCenter / maxDistance * 20);
      } else {
        // نقاط متدرجة للأفلام خارج النطاق
        final distance =
            movieYear < minYear ? minYear - movieYear : movieYear - maxYear;
        return (80.0 - (distance * 3)).clamp(0.0, 80.0);
      }
    } catch (e) {
      return 40.0;
    }
  }

  // حساب نقاط العنوان المحسن والمطور مع تركيز على النوعية
  double _calculateAdvancedTitleScore(
    MovieModel movie,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
  ) {
    if (movie.title == null) return 0.0;

    double score = 0.0;
    final movieTitle = movie.title!.toLowerCase();

    // 1. التشابه المباشر مع عناوين الأفلام المختارة (40% من نقاط العنوان)
    double titleSimilarityScore = 0.0;
    for (final selectedMovie in selectedMovies) {
      if (selectedMovie.title != null) {
        final selectedTitle = selectedMovie.title!.toLowerCase();
        final similarity =
            _calculateEnhancedStringSimilarity(movieTitle, selectedTitle);
        titleSimilarityScore += similarity;
      }
    }
    if (selectedMovies.isNotEmpty) {
      score += (titleSimilarityScore / selectedMovies.length) * 40;
    }

    // 2. تحليل الكلمات المفتاحية المرتبطة بالنوعية (30% من نقاط العنوان) - زيادة كبيرة
    score += _calculateGenreBasedTitleScore(
            movieTitle, movie.genreIds ?? [], selectedMovies) *
        30;

    // 3. تحليل السلاسل والامتدادات (15% من نقاط العنوان)
    score += _calculateSeriesAndSequelScore(movieTitle, selectedMovies) * 15;

    // 4. التشابه مع الكلمات المفتاحية المستخرجة (10% من نقاط العنوان)
    final keywords = userPreferences['keywords'] as List<String>;
    if (keywords.isNotEmpty) {
      double keywordScore = 0.0;
      for (final keyword in keywords) {
        if (movieTitle.contains(keyword)) {
          final keywordWeight = keyword.length > 4 ? 2.5 : 1.2;
          keywordScore += keywordWeight;
        }
      }
      score += (keywordScore / keywords.length) * 10;
    }

    // 5. تحليل الكلمات المشتركة المهمة (5% من نقاط العنوان)
    score += _calculateImportantWordsScore(movieTitle, selectedMovies) * 5;

    return score.clamp(0.0, 100.0);
  }

  // حساب نقاط العنوان بناءً على النوعية
  double _calculateGenreBasedTitleScore(
      String title, List<int> genreIds, List<MovieModel> selectedMovies) {
    if (genreIds.isEmpty) return 0.0;

    double genreScore = 0.0;

    // 1. كلمات مفتاحية مرتبطة بكل نوعية مع أوزان مختلفة (محدثة ودقيقة)
    final Map<int, Map<String, double>> genreKeywordsWithWeights = {
      28: {
        // Action
        'action': 3.0,
        'fight': 2.5,
        'battle': 2.5,
        'war': 2.0,
        'combat': 2.0,
        'hero': 1.5,
        'mission': 1.5,
        'chase': 1.5,
        'explosion': 1.5
      },
      12: {
        // Adventure
        'adventure': 3.0,
        'journey': 2.5,
        'quest': 2.5,
        'explore': 2.0,
        'treasure': 2.0,
        'island': 1.5,
        'expedition': 1.5,
        'discovery': 1.5
      },
      16: {
        // Animation
        'animated': 3.0,
        'animation': 3.0,
        'cartoon': 2.5,
        'family': 2.0,
        'kids': 2.0,
        'children': 2.0,
        'disney': 1.5,
        'pixar': 1.5,
        'dreamworks': 1.5
      },
      35: {
        // Comedy
        'comedy': 3.0,
        'funny': 2.5,
        'laugh': 2.0,
        'humor': 2.0,
        'comic': 2.0,
        'hilarious': 1.5,
        'fun': 1.5,
        'joke': 1.5
      },
      80: {
        // Crime
        'crime': 3.0,
        'criminal': 2.5,
        'police': 2.5,
        'detective': 2.0,
        'murder': 2.0,
        'gang': 1.5,
        'mafia': 1.5,
        'heist': 1.5
      },
      99: {
        // Documentary
        'documentary': 3.0,
        'real': 2.5,
        'true': 2.5,
        'story': 2.0,
        'life': 2.0,
        'history': 1.5,
        'behind': 1.5
      },
      18: {
        // Drama
        'drama': 3.0,
        'life': 2.5,
        'story': 2.0,
        'emotional': 2.0,
        'relationship': 1.5,
        'heart': 1.5,
        'soul': 1.5
      },
      10751: {
        // Family
        'family': 3.0,
        'kids': 2.5,
        'children': 2.5,
        'parents': 2.0,
        'home': 2.0,
        'together': 1.5,
        'wholesome': 1.5
      },
      14: {
        // Fantasy
        'fantasy': 3.0,
        'magic': 2.5,
        'magical': 2.5,
        'wizard': 2.0,
        'dragon': 2.0,
        'fairy': 1.5,
        'enchanted': 1.5,
        'mystical': 1.5
      },
      36: {
        // History
        'history': 3.0,
        'historical': 2.5,
        'past': 2.0,
        'ancient': 2.0,
        'period': 1.5,
        'era': 1.5,
        'century': 1.5
      },
      27: {
        // Horror
        'horror': 3.0,
        'scary': 2.5,
        'fear': 2.0,
        'terror': 2.0,
        'nightmare': 1.5,
        'haunted': 1.5,
        'ghost': 1.5,
        'evil': 1.5,
        'dark': 1.5
      },
      10402: {
        // Music
        'music': 3.0,
        'musical': 2.5,
        'song': 2.0,
        'dance': 2.0,
        'singer': 1.5,
        'band': 1.5,
        'concert': 1.5
      },
      9648: {
        // Mystery
        'mystery': 3.0,
        'secret': 2.5,
        'unknown': 2.0,
        'hidden': 2.0,
        'puzzle': 1.5,
        'clue': 1.5,
        'investigate': 1.5
      },
      10749: {
        // Romance
        'romance': 3.0,
        'love': 2.5,
        'romantic': 2.5,
        'heart': 2.0,
        'kiss': 1.5,
        'wedding': 1.5,
        'couple': 1.5,
        'passion': 1.5
      },
      878: {
        // Science Fiction
        'science': 2.5,
        'fiction': 2.5,
        'future': 2.0,
        'space': 2.0,
        'alien': 2.0,
        'robot': 1.5,
        'cyber': 1.5,
        'technology': 1.5
      },
      10770: {
        // TV Movie
        'tv': 2.0,
        'television': 2.0,
        'movie': 1.5,
        'special': 1.5
      },
      53: {
        // Thriller
        'thriller': 3.0,
        'suspense': 2.5,
        'tension': 2.0,
        'dangerous': 2.0,
        'intense': 1.5,
        'edge': 1.5,
        'psychological': 1.5
      },
      10752: {
        // War
        'war': 3.0,
        'battle': 2.5,
        'soldier': 2.5,
        'military': 2.0,
        'army': 2.0,
        'combat': 1.5,
        'conflict': 1.5
      },
      37: {
        // Western
        'western': 3.0,
        'cowboy': 2.5,
        'desert': 2.0,
        'frontier': 2.0,
        'ranch': 1.5,
        'outlaw': 1.5,
        'sheriff': 1.5
      },
    };

    // 2. تحليل العنوان للكلمات المفتاحية
    final titleWords = title
        .toLowerCase()
        .split(RegExp(r'[^a-zA-Z0-9]+'))
        .where((w) => w.length > 2);

    for (final genreId in genreIds) {
      final keywordsMap = genreKeywordsWithWeights[genreId] ?? {};
      for (final titleWord in titleWords) {
        for (final entry in keywordsMap.entries) {
          final keyword = entry.key;
          final weight = entry.value;

          if (titleWord == keyword) {
            genreScore += weight * 10; // تطابق كامل
          } else if (titleWord.contains(keyword) ||
              keyword.contains(titleWord)) {
            genreScore += weight * 6; // تطابق جزئي
          } else if (_calculateSimpleLevenshtein(titleWord, keyword) <= 2) {
            genreScore += weight * 3; // تشابه قريب
          }
        }
      }
    }

    // 3. تحليل تطابق النوعية مع الأفلام المختارة
    double genreMatchBonus = 0.0;
    for (final selectedMovie in selectedMovies) {
      if (selectedMovie.genreIds != null) {
        final commonGenres =
            genreIds.where((id) => selectedMovie.genreIds!.contains(id)).length;
        if (commonGenres > 0) {
          genreMatchBonus += (commonGenres / genreIds.length) * 20;
        }
      }
    }

    if (selectedMovies.isNotEmpty) {
      genreScore += genreMatchBonus / selectedMovies.length;
    }

    return genreScore.clamp(0.0, 100.0);
  }

  // حساب مسافة Levenshtein مبسطة
  int _calculateSimpleLevenshtein(String s1, String s2) {
    if (s1 == s2) return 0;
    if (s1.isEmpty) return s2.length;
    if (s2.isEmpty) return s1.length;
    if ((s1.length - s2.length).abs() > 3) return 999; // تحسين الأداء

    List<int> previousRow = List.generate(s2.length + 1, (i) => i);

    for (int i = 0; i < s1.length; i++) {
      List<int> currentRow = [i + 1];

      for (int j = 0; j < s2.length; j++) {
        int cost = s1[i] == s2[j] ? 0 : 1;
        currentRow.add([
          currentRow[j] + 1,
          previousRow[j + 1] + 1,
          previousRow[j] + cost,
        ].reduce((a, b) => a < b ? a : b));
      }

      previousRow = currentRow;
    }

    return previousRow.last;
  }

  // حساب التشابه المحسن بين النصوص
  double _calculateEnhancedStringSimilarity(String str1, String str2) {
    if (str1 == str2) return 100.0;

    // تطبيع النصوص
    final normalizedStr1 = _normalizeMovieTitle(str1);
    final normalizedStr2 = _normalizeMovieTitle(str2);

    if (normalizedStr1 == normalizedStr2) return 95.0;

    // تحليل الكلمات
    final words1 = normalizedStr1.split(' ').where((w) => w.length > 2).toSet();
    final words2 = normalizedStr2.split(' ').where((w) => w.length > 2).toSet();

    if (words1.isEmpty && words2.isEmpty) return 100.0;
    if (words1.isEmpty || words2.isEmpty) return 0.0;

    // حساب Jaccard similarity
    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;
    final jaccardSimilarity = intersection / union;

    // حساب نسبة الكلمات المشتركة
    final commonWordsRatio =
        intersection / words1.length.clamp(1, double.infinity);

    // حساب التشابه في بداية العنوان (مهم للسلاسل)
    final startSimilarity =
        _calculateStartSimilarity(normalizedStr1, normalizedStr2);

    // حساب التشابه الصوتي (للأسماء المتشابهة صوتياً)
    final phoneticSimilarity =
        _calculatePhoneticSimilarity(normalizedStr1, normalizedStr2);

    // حساب التشابه في الطول (الأسماء المتشابهة غالباً لها أطوال متقاربة)
    final lengthSimilarity =
        _calculateLengthSimilarity(normalizedStr1, normalizedStr2);

    // دمج النتائج مع أوزان محسنة لصالح التشابه في الأسماء
    final finalScore = (jaccardSimilarity * 40) + // تقليل قليل
        (commonWordsRatio * 25) + // تقليل قليل
        (startSimilarity * 20) + // نفس القيمة
        (phoneticSimilarity * 10) + // جديد
        (lengthSimilarity * 5); // جديد

    return finalScore.clamp(0.0, 100.0);
  }

  // تطبيع عنوان الفيلم للمقارنة
  String _normalizeMovieTitle(String title) {
    return title
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), ' ') // استبدال علامات الترقيم بمسافات
        .replaceAll(RegExp(r'\b(the|a|an|and|or|of|in|on|at|to|for|with|by)\b'),
            '') // إزالة كلمات الربط
        .replaceAll(RegExp(r'\s+'), ' ') // توحيد المسافات
        .trim();
  }

  // حساب التشابه في بداية العنوان
  double _calculateStartSimilarity(String str1, String str2) {
    final minLength =
        [str1.length, str2.length, 10].reduce((a, b) => a < b ? a : b);
    if (minLength == 0) return 0.0;

    int matchingChars = 0;
    for (int i = 0; i < minLength; i++) {
      if (str1[i] == str2[i]) {
        matchingChars++;
      } else {
        break;
      }
    }

    return (matchingChars / minLength) * 100;
  }

  // حساب التشابه الصوتي البسيط
  double _calculatePhoneticSimilarity(String str1, String str2) {
    // تحويل إلى أحرف صوتية مبسطة
    final phonetic1 = _toSimplePhonetic(str1);
    final phonetic2 = _toSimplePhonetic(str2);

    if (phonetic1 == phonetic2) return 100.0;
    if (phonetic1.isEmpty || phonetic2.isEmpty) return 0.0;

    // حساب التشابه في الأحرف الصوتية
    final minLength =
        [phonetic1.length, phonetic2.length].reduce((a, b) => a < b ? a : b);
    int matchingChars = 0;

    for (int i = 0; i < minLength; i++) {
      if (phonetic1[i] == phonetic2[i]) {
        matchingChars++;
      }
    }

    return (matchingChars / phonetic1.length.clamp(1, double.infinity)) * 100;
  }

  // تحويل النص إلى تمثيل صوتي مبسط
  String _toSimplePhonetic(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[aeiou]'), '') // إزالة الحروف المتحركة
        .replaceAll(RegExp(r'[^a-z]'), '') // الاحتفاظ بالأحرف الساكنة فقط
        .replaceAll(RegExp(r'(.)\1+'), r'$1'); // إزالة التكرار
  }

  // حساب التشابه في الطول
  double _calculateLengthSimilarity(String str1, String str2) {
    if (str1.isEmpty && str2.isEmpty) return 100.0;
    if (str1.isEmpty || str2.isEmpty) return 0.0;

    final length1 = str1.length;
    final length2 = str2.length;
    final maxLength = [length1, length2].reduce((a, b) => a > b ? a : b);
    final minLength = [length1, length2].reduce((a, b) => a < b ? a : b);

    // كلما كان الفرق أقل، كان التشابه أعلى
    final similarity = (minLength / maxLength) * 100;
    return similarity;
  }

  // حساب نقاط السلاسل والامتدادات
  double _calculateSeriesAndSequelScore(
      String movieTitle, List<MovieModel> selectedMovies) {
    double score = 0.0;

    // كلمات تدل على السلاسل والامتدادات
    final seriesKeywords = [
      'part',
      'episode',
      'chapter',
      'volume',
      'season',
      'ii',
      'iii',
      'iv',
      'v',
      'vi',
      'vii',
      'viii',
      'ix',
      'x',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      'two',
      'three',
      'four',
      'five',
      'six',
      'seven',
      'eight',
      'nine',
      'ten',
      'sequel',
      'prequel',
      'returns',
      'revenge',
      'rise',
      'dawn',
      'war',
      'begins',
      'origins',
      'legacy',
      'reborn',
      'resurrection'
    ];

    for (final selectedMovie in selectedMovies) {
      if (selectedMovie.title != null) {
        final selectedTitle = selectedMovie.title!.toLowerCase();

        // البحث عن كلمات السلاسل
        bool hasSeriesIndicator = false;
        for (final keyword in seriesKeywords) {
          if (movieTitle.contains(keyword) || selectedTitle.contains(keyword)) {
            hasSeriesIndicator = true;
            break;
          }
        }

        if (hasSeriesIndicator) {
          // إذا كان هناك مؤشر على السلسلة، ابحث عن الجزء الأساسي من الاسم
          final baseTitle1 = _extractBaseTitleFromSeries(movieTitle);
          final baseTitle2 = _extractBaseTitleFromSeries(selectedTitle);

          if (baseTitle1.isNotEmpty && baseTitle2.isNotEmpty) {
            final baseSimilarity =
                _calculateEnhancedStringSimilarity(baseTitle1, baseTitle2);
            score += baseSimilarity;
          }
        }
      }
    }

    return selectedMovies.isNotEmpty ? score / selectedMovies.length : 0.0;
  }

  // استخراج العنوان الأساسي من السلسلة
  String _extractBaseTitleFromSeries(String title) {
    // إزالة الأرقام والكلمات الدالة على التسلسل من نهاية العنوان
    final cleanTitle = title
        .replaceAll(
            RegExp(r'\b(part|episode|chapter|volume|season)\s*\d+\b'), '')
        .replaceAll(RegExp(r'\b(ii|iii|iv|v|vi|vii|viii|ix|x)\b'), '')
        .replaceAll(RegExp(r'\b\d+\b'), '')
        .replaceAll(
            RegExp(r'\b(two|three|four|five|six|seven|eight|nine|ten)\b'), '')
        .replaceAll(
            RegExp(
                r'\b(sequel|prequel|returns|revenge|rise|dawn|war|begins|origins|legacy|reborn|resurrection)\b'),
            '')
        .replaceAll(
            RegExp(r'[:\-–—].*$'), '') // إزالة كل شيء بعد النقطتين أو الشرطة
        .trim();

    return cleanTitle;
  }

  // حساب نقاط الكلمات المهمة المشتركة
  double _calculateImportantWordsScore(
      String movieTitle, List<MovieModel> selectedMovies) {
    double score = 0.0;

    // كلمات مهمة تدل على نوع الفيلم أو الموضوع
    final importantWords = _extractImportantWords(movieTitle);

    if (importantWords.isEmpty) return 0.0;

    for (final selectedMovie in selectedMovies) {
      if (selectedMovie.title != null) {
        final selectedImportantWords =
            _extractImportantWords(selectedMovie.title!.toLowerCase());

        final commonImportantWords =
            importantWords.intersection(selectedImportantWords).length;
        if (commonImportantWords > 0) {
          score += (commonImportantWords / importantWords.length) * 100;
        }
      }
    }

    return selectedMovies.isNotEmpty ? score / selectedMovies.length : 0.0;
  }

  // استخراج الكلمات المهمة من العنوان
  Set<String> _extractImportantWords(String title) {
    final words = title.toLowerCase().split(' ');
    final importantWords = <String>{};

    // كلمات مهمة تدل على الموضوع أو النوع
    final significantWords = [
      'war',
      'battle',
      'fight',
      'hero',
      'super',
      'man',
      'woman',
      'king',
      'queen',
      'dark',
      'light',
      'black',
      'white',
      'red',
      'blue',
      'green',
      'gold',
      'silver',
      'dragon',
      'knight',
      'warrior',
      'soldier',
      'captain',
      'commander',
      'general',
      'magic',
      'wizard',
      'witch',
      'spell',
      'curse',
      'power',
      'force',
      'energy',
      'space',
      'star',
      'galaxy',
      'planet',
      'earth',
      'world',
      'universe',
      'cosmic',
      'time',
      'future',
      'past',
      'ancient',
      'modern',
      'new',
      'old',
      'young',
      'love',
      'heart',
      'soul',
      'spirit',
      'mind',
      'dream',
      'nightmare',
      'hope',
      'death',
      'life',
      'blood',
      'fire',
      'water',
      'ice',
      'storm',
      'thunder',
      'secret',
      'mystery',
      'hidden',
      'lost',
      'found',
      'treasure',
      'quest',
      'journey'
    ];

    for (final word in words) {
      if (word.length > 4 || significantWords.contains(word)) {
        importantWords.add(word);
      }
    }

    return importantWords;
  }

  // حساب نقاط الشعبية المحسن
  double _calculateAdvancedPopularityScore(MovieModel movie) {
    double score = 0.0;

    // 1. نقاط التقييم مع منحنى محسن
    if (movie.voteAverage != null) {
      if (movie.voteAverage! >= 8.0) {
        score += 50.0;
      } else if (movie.voteAverage! >= 7.0) {
        score += 40.0;
      } else if (movie.voteAverage! >= 6.0) {
        score += 30.0;
      } else if (movie.voteAverage! >= 5.0) {
        score += 20.0;
      } else {
        score += 10.0;
      }
    }

    // 2. نقاط الشعبية مع تطبيع أفضل
    if (movie.popularity != null) {
      if (movie.popularity! >= 50) {
        score += 30.0;
      } else if (movie.popularity! >= 20) {
        score += 25.0;
      } else if (movie.popularity! >= 10) {
        score += 20.0;
      } else if (movie.popularity! >= 5) {
        score += 15.0;
      } else {
        score += 10.0;
      }
    }

    // 3. نقاط إضافية لعدد الأصوات
    if (movie.voteCount != null) {
      if (movie.voteCount! >= 5000) {
        score += 20.0;
      } else if (movie.voteCount! >= 1000) {
        score += 15.0;
      } else if (movie.voteCount! >= 500) {
        score += 10.0;
      } else if (movie.voteCount! >= 100) {
        score += 5.0;
      }
    }

    return score.clamp(0.0, 100.0);
  }

  // حساب التشابه المباشر مع الأفلام المختارة مع تركيز على النوعية
  double _calculateDirectSimilarityScore(
    MovieModel movie,
    List<MovieModel> selectedMovies,
  ) {
    double totalSimilarity = 0.0;
    int comparisons = 0;

    for (final selectedMovie in selectedMovies) {
      double similarity = 0.0;

      // 1. تشابه النوعية (55% من النقاط) - زيادة كبيرة
      if (movie.genreIds != null && selectedMovie.genreIds != null) {
        final commonGenres = movie.genreIds!
            .where((id) => selectedMovie.genreIds!.contains(id))
            .length;

        if (commonGenres > 0) {
          // حساب نسبة التطابق مع وزن أكبر للنوعية الرئيسية
          final movieGenreRatio = commonGenres / movie.genreIds!.length;
          final selectedGenreRatio =
              commonGenres / selectedMovie.genreIds!.length;
          final averageRatio = (movieGenreRatio + selectedGenreRatio) / 2;

          similarity += averageRatio * 55;

          // نقاط إضافية للنوعية الرئيسية المتطابقة
          if (movie.genreIds!.isNotEmpty &&
              selectedMovie.genreIds!.isNotEmpty) {
            if (movie.genreIds!.first == selectedMovie.genreIds!.first) {
              similarity += 10; // نقاط إضافية للنوعية الرئيسية
            }
          }
        }
      }

      // 2. تشابه التقييم (25% من النقاط) - تقليل
      if (movie.voteAverage != null && selectedMovie.voteAverage != null) {
        final ratingDiff =
            (movie.voteAverage! - selectedMovie.voteAverage!).abs();
        final ratingScore = (2.5 - ratingDiff.clamp(0.0, 2.5)) / 2.5;
        similarity += ratingScore * 25;
      }

      // 3. تشابه السنة (20% من النقاط) - تقليل
      if (movie.releaseDate != null && selectedMovie.releaseDate != null) {
        try {
          final movieYear = int.parse(movie.releaseDate!.substring(0, 4));
          final selectedYear =
              int.parse(selectedMovie.releaseDate!.substring(0, 4));
          final yearDiff = (movieYear - selectedYear).abs();
          final yearScore = (15.0 - yearDiff.clamp(0.0, 15.0)) / 15.0;
          similarity += yearScore * 20;
        } catch (e) {
          // تجاهل أخطاء تحليل التاريخ
        }
      }

      totalSimilarity += similarity;
      comparisons++;
    }

    return comparisons > 0 ? totalSimilarity / comparisons : 0.0;
  }

  // حساب التشابه بين النصوص
  double _calculateStringSimilarity(String str1, String str2) {
    if (str1 == str2) return 1.0;

    final words1 = str1.split(' ').where((w) => w.length > 2).toSet();
    final words2 = str2.split(' ').where((w) => w.length > 2).toSet();

    if (words1.isEmpty && words2.isEmpty) return 1.0;
    if (words1.isEmpty || words2.isEmpty) return 0.0;

    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;

    return intersection / union;
  }

  // ===== دوال محسنة لضمان 5 توصيات =====

  // إضافة أفلام من مصادر متعددة
  Future<void> _addMoviesFromMultipleSources(
    ApiService apiService,
    Set<MovieModel> allCandidates,
    Map<String, dynamic> userPreferences,
  ) async {
    try {
      // 1. أفلام من نفس النوعية
      await _addMoviesByGenre(apiService, allCandidates, userPreferences);

      // 2. أفلام شائعة حديثة
      final nowPlayingMovies =
          await apiService.getMovieData(MovieType.nowPlaying);
      allCandidates.addAll(nowPlayingMovies);
      print('🎬 تم إضافة ${nowPlayingMovies.length} فيلم من الأفلام الحالية');

      // 3. أفلام قادمة
      final upcomingMovies = await apiService.getMovieData(MovieType.upcoming);
      allCandidates.addAll(upcomingMovies);
      print('🔮 تم إضافة ${upcomingMovies.length} فيلم من الأفلام القادمة');

      // 4. أفلام إضافية من الأعلى تقييماً
      final topRatedMovies = await apiService.getMovieData(MovieType.topRated);
      allCandidates.addAll(topRatedMovies);
      print('⭐ تم إضافة ${topRatedMovies.length} فيلم من الأعلى تقييماً');
    } catch (e) {
      print('❌ خطأ في إضافة أفلام من مصادر متعددة: $e');
    }
  }

  // ضمان الحصول على 5 توصيات بالضبط
  Future<List<MovieModel>> _ensureExactly5Recommendations(
    List<MovieModel> candidates,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
    ApiService apiService,
  ) async {
    print('🎯 بدء عملية ضمان 5 توصيات من ${candidates.length} مرشح');

    // المرحلة 1: تطبيق الخوارزمية الذكية
    List<MovieModel> recommendations = _applySmartRecommendationAlgorithm(
      candidates,
      selectedMovies,
      userPreferences,
    );

    print('📊 المرحلة 1: حصلنا على ${recommendations.length} توصية');

    // المرحلة 2: إذا كان لدينا أقل من 5، نضيف المزيد
    if (recommendations.length < 5) {
      print('⚠️ نحتاج المزيد من التوصيات، البحث عن أفلام إضافية...');

      final additionalMovies = await _getAdditionalMovies(
        apiService,
        selectedMovies,
        userPreferences,
        recommendations,
        5 - recommendations.length,
      );

      recommendations.addAll(additionalMovies);
      print('➕ تم إضافة ${additionalMovies.length} فيلم إضافي');
    }

    // المرحلة 3: إذا كان لدينا أكثر من 5، نختار الأفضل
    if (recommendations.length > 5) {
      recommendations = recommendations.take(5).toList();
      print('✂️ تم تقليم القائمة إلى 5 أفلام');
    }

    // المرحلة 4: التحقق النهائي وإضافة أفلام احتياطية إذا لزم الأمر
    if (recommendations.length < 5) {
      print(
          '🚨 ما زلنا نحتاج ${5 - recommendations.length} أفلام، إضافة أفلام احتياطية...');

      final backupMovies = await _getBackupMovies(
        apiService,
        recommendations,
        5 - recommendations.length,
      );

      recommendations.addAll(backupMovies);
      print('🔄 تم إضافة ${backupMovies.length} فيلم احتياطي');
    }

    // ضمان عدم تجاوز 5 أفلام
    final finalList = recommendations.take(5).toList();

    print('✅ النتيجة النهائية: ${finalList.length} أفلام');
    for (int i = 0; i < finalList.length; i++) {
      print('${i + 1}. ${finalList[i].title}');
    }

    return finalList;
  }

  // الحصول على أفلام إضافية
  Future<List<MovieModel>> _getAdditionalMovies(
    ApiService apiService,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
    List<MovieModel> existingRecommendations,
    int needed,
  ) async {
    final List<MovieModel> additionalMovies = [];
    final existingIds = existingRecommendations.map((m) => m.id).toSet();
    final selectedIds = selectedMovies.map((m) => m.id).toSet();

    try {
      // البحث في الأفلام الشائعة
      final popularMovies = await apiService.getMovieData(MovieType.popular);

      for (final movie in popularMovies) {
        if (additionalMovies.length >= needed) break;

        if (movie.id != null &&
            !existingIds.contains(movie.id) &&
            !selectedIds.contains(movie.id)) {
          // تحقق من التوافق مع التفضيلات
          if (_isMovieCompatible(movie, userPreferences)) {
            additionalMovies.add(movie);
            existingIds.add(movie.id!);
          }
        }
      }

      // إذا ما زلنا نحتاج المزيد، ابحث في الأفلام الأعلى تقييماً
      if (additionalMovies.length < needed) {
        final topRatedMovies =
            await apiService.getMovieData(MovieType.topRated);

        for (final movie in topRatedMovies) {
          if (additionalMovies.length >= needed) break;

          if (movie.id != null &&
              !existingIds.contains(movie.id) &&
              !selectedIds.contains(movie.id)) {
            additionalMovies.add(movie);
            existingIds.add(movie.id!);
          }
        }
      }
    } catch (e) {
      print('❌ خطأ في الحصول على أفلام إضافية: $e');
    }

    return additionalMovies;
  }

  // الحصول على أفلام احتياطية
  Future<List<MovieModel>> _getBackupMovies(
    ApiService apiService,
    List<MovieModel> existingRecommendations,
    int needed,
  ) async {
    final List<MovieModel> backupMovies = [];
    final existingIds = existingRecommendations.map((m) => m.id).toSet();

    try {
      // استخدام الأفلام الشائعة كاحتياطي
      final popularMovies = await apiService.getMovieData(MovieType.popular);

      for (final movie in popularMovies) {
        if (backupMovies.length >= needed) break;

        if (movie.id != null && !existingIds.contains(movie.id)) {
          backupMovies.add(movie);
          existingIds.add(movie.id!);
        }
      }
    } catch (e) {
      print('❌ خطأ في الحصول على أفلام احتياطية: $e');
    }

    return backupMovies;
  }

  // فحص توافق الفيلم مع التفضيلات
  bool _isMovieCompatible(
      MovieModel movie, Map<String, dynamic> userPreferences) {
    // فحص النوعية
    final preferredGenres = userPreferences['preferredGenres'] as List<int>;
    if (preferredGenres.isNotEmpty && movie.genreIds != null) {
      final hasMatchingGenre =
          movie.genreIds!.any((id) => preferredGenres.contains(id));
      if (hasMatchingGenre) return true;
    }

    // فحص التقييم
    final averageRating = userPreferences['averageRating'] as double;
    if (movie.voteAverage != null) {
      final ratingDiff = (movie.voteAverage! - averageRating).abs();
      if (ratingDiff <= 2.0) return true;
    }

    // فحص السنة
    final yearRange = userPreferences['yearRange'] as Map<String, dynamic>;
    if (movie.releaseDate != null && movie.releaseDate!.isNotEmpty) {
      try {
        final movieYear = int.parse(movie.releaseDate!.substring(0, 4));
        final minYear = yearRange['min'] as int;
        final maxYear = yearRange['max'] as int;
        if (movieYear >= minYear - 10 && movieYear <= maxYear + 5) return true;
      } catch (e) {
        // تجاهل أخطاء تحليل التاريخ
      }
    }

    // إذا لم يتطابق مع أي معيار، لا يزال مقبولاً كاحتياطي
    return false;
  }
}
