import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:movie_proj/core/const.dart';
import 'package:movie_proj/core/my_colors.dart';
import 'package:movie_proj/core/my_styles.dart';
import 'package:movie_proj/core/spacing.dart';
import 'package:movie_proj/core/service/service.dart';
import 'package:movie_proj/core/service/enums.dart';
import 'package:movie_proj/feature/details/details_screen.dart';
import 'package:movie_proj/feature/home/<USER>/movie_model.dart';
import 'package:movie_proj/feature/suggest/manage/suggest_search_cubit.dart';

class SuggestBody extends StatefulWidget {
  const SuggestBody({super.key});

  @override
  State<SuggestBody> createState() => _SuggestBodyState();
}

class _SuggestBodyState extends State<SuggestBody> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounceTimer;
  final Set<MovieModel> _selectedMovies = {};

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // Search change handler
  void _onSearchChanged(String query) {
    setState(() {}); // Update UI for search icon animation
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (query.isNotEmpty) {
        context.read<SuggestSearchCubit>().searchMovies(query);
      } else {
        context.read<SuggestSearchCubit>().clearSearch();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MyColors.primaryColor,
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              vSpace(20),
              // For Your Taste Section
              _buildForYourTasteSection(),
              vSpace(40),
              // Recommendations Section with animation
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 600),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 0.3),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeOutCubic,
                    )),
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  );
                },
                child: _selectedMovies.isNotEmpty
                    ? _buildRecommendationsSection()
                    : _buildEmptyRecommendationsState(),
              ),
              vSpace(40),
            ],
          ),
        ),
      ),
    );
  }

  // Empty Recommendations State
  Widget _buildEmptyRecommendationsState() {
    return Container(
      key: const ValueKey('empty'),
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey[800]!.withOpacity(0.3),
            Colors.grey[900]!.withOpacity(0.2),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.grey[700]!.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[700]!.withOpacity(0.3),
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              Icons.movie_filter_outlined,
              size: 48,
              color: Colors.grey[500],
            ),
          ),
          vSpace(20),
          Text(
            'Select Movies to Get Recommendations',
            style: MyStyles.title24White700.copyWith(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[400],
            ),
            textAlign: TextAlign.center,
          ),
          vSpace(8),
          Text(
            'Choose up to 5 movies or TV series above to discover personalized recommendations based on your taste.',
            style: MyStyles.title24White400.copyWith(
              fontSize: 14,
              color: Colors.grey[500],
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // For Your Taste Section
  Widget _buildForYourTasteSection() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            MyColors.primaryColor,
            MyColors.primaryColor.withOpacity(0.95),
          ],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with animation
            AnimatedContainer(
              duration: const Duration(milliseconds: 800),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title with gradient
                  ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      colors: [Colors.white, Colors.blue, Colors.purple],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ).createShader(bounds),
                    child: Text(
                      'For Your Taste',
                      style: MyStyles.title24White700.copyWith(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  vSpace(12),
                  // Subtitle with better styling
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'Choose 5 Movies or TV Series To Have A Recommend According On Your Taste',
                      style: MyStyles.title24White400.copyWith(
                        fontSize: 16,
                        color: Colors.grey[300],
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            vSpace(40),
            // Search Bar
            _buildSearchBar(),
            vSpace(32),
            // Selection Counter
            _buildSelectionCounter(),
            vSpace(24),
            // Selected Movies Grid
            _buildSelectedMoviesGrid(),
          ],
        ),
      ),
    );
  }

  // Selection Counter Widget
  Widget _buildSelectionCounter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.withOpacity(0.2),
            Colors.purple.withOpacity(0.2),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: Colors.blue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.movie_filter_outlined,
                  color: Colors.blue[300],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Selected Movies',
                    style: MyStyles.title24White700.copyWith(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '${_selectedMovies.length} of 5 selected',
                    style: MyStyles.title24White400.copyWith(
                      fontSize: 12,
                      color: Colors.grey[400],
                    ),
                  ),
                ],
              ),
            ],
          ),
          // Progress indicator
          Container(
            width: 80,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: _selectedMovies.length / 5,
              child: Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colors.blue, Colors.purple],
                  ),
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Search Bar Widget
  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            MyColors.secondaryColor,
            MyColors.secondaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // All Dropdown with better styling
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue.withOpacity(0.2),
                  Colors.blue.withOpacity(0.1),
                ],
              ),
              border: Border(
                right: BorderSide(
                  color: Colors.blue.withOpacity(0.3),
                  width: 1,
                ),
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.tune_rounded,
                  color: Colors.blue[300],
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  'All',
                  style: MyStyles.title24White400.copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[300],
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.keyboard_arrow_down_rounded,
                  color: Colors.blue[300],
                  size: 20,
                ),
              ],
            ),
          ),
          // Search Field with better styling
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: TextField(
                controller: _searchController,
                style: MyStyles.title24White400.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                decoration: InputDecoration(
                  hintText: 'Search for movies, TV shows...',
                  hintStyle: MyStyles.title24White400.copyWith(
                    fontSize: 16,
                    color: Colors.grey[400],
                    fontWeight: FontWeight.w400,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 16),
                ),
                onChanged: _onSearchChanged,
              ),
            ),
          ),
          // Search Icon with animation
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: _searchController.text.isNotEmpty
                  ? const LinearGradient(
                      colors: [Colors.blue, Colors.purple],
                    )
                  : null,
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: Icon(
              _searchController.text.isNotEmpty
                  ? Icons.search_rounded
                  : Icons.search_outlined,
              color: _searchController.text.isNotEmpty
                  ? Colors.white
                  : Colors.grey[400],
              size: 22,
            ),
          ),
        ],
      ),
    );
  }

  // Selected Movies Grid
  Widget _buildSelectedMoviesGrid() {
    return Column(
      children: [
        // Single Row - All Selected Movies (5 slots)
        SizedBox(
          height: 140, // Reduced height
          child: Row(
            children: [
              // All 5 movie slots in one row
              ...List.generate(
                5,
                (index) => Expanded(
                  child: Container(
                    margin: EdgeInsets.only(
                      right: index < 4 ? 12 : 0, // Reduced margin
                    ),
                    child: _selectedMovies.length > index
                        ? _buildMovieCard(
                            _selectedMovies.elementAt(index), true)
                        : _buildEmptyMovieSlot(),
                  ),
                ),
              ),
            ],
          ),
        ),
        vSpace(24),
        // Search Results
        _buildSearchResults(),
      ],
    );
  }

  // Recommendations Section
  Widget _buildRecommendationsSection() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 800),
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.purple.withOpacity(0.1),
            Colors.blue.withOpacity(0.1),
            Colors.indigo.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.purple.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Colors.purple, Colors.blue],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.purple.withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.auto_awesome_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ShaderMask(
                        shaderCallback: (bounds) => const LinearGradient(
                          colors: [Colors.purple, Colors.blue, Colors.indigo],
                        ).createShader(bounds),
                        child: Text(
                          'Smart Recommendations',
                          style: MyStyles.title24White700.copyWith(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Top 5 recommendations based on your taste',
                        style: MyStyles.title24White400.copyWith(
                          fontSize: 14,
                          color: Colors.grey[400],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            vSpace(32),
            // Recommendations Grid
            _buildRecommendationsGrid(),
          ],
        ),
      ),
    );
  }

  // Movie Card Widget
  Widget _buildMovieCard(MovieModel movie, bool isSelected) {
    final canSelect = _selectedMovies.length < 5 || isSelected;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: GestureDetector(
        onTap: canSelect
            ? () {
                setState(() {
                  if (isSelected) {
                    _selectedMovies.remove(movie);
                  } else {
                    _selectedMovies.add(movie);
                  }
                });
              }
            : null,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: isSelected
                ? LinearGradient(
                    colors: [
                      Colors.blue.withOpacity(0.3),
                      Colors.purple.withOpacity(0.2),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
            border: Border.all(
              color: isSelected
                  ? Colors.blue
                  : canSelect
                      ? Colors.grey[700]!
                      : Colors.grey[800]!,
              width: isSelected ? 2.5 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: isSelected
                    ? Colors.blue.withOpacity(0.3)
                    : Colors.black.withOpacity(0.2),
                blurRadius: isSelected ? 15 : 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Stack(
              children: [
                // Movie Poster
                if (movie.posterPath != null)
                  Positioned.fill(
                    child: Image.network(
                      '$kmoviedbImageURL${movie.posterPath}',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildPlaceholderImage();
                      },
                    ),
                  )
                else
                  _buildPlaceholderImage(),

                // Overlay gradient
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                        stops: const [0.5, 1.0],
                      ),
                    ),
                  ),
                ),

                // Movie Title
                Positioned(
                  bottom: 8,
                  left: 8,
                  right: 8,
                  child: Text(
                    movie.title ?? 'Unknown',
                    style: MyStyles.title24White400.copyWith(
                      fontSize: 10, // Smaller font size
                      fontWeight: FontWeight.w700,
                      shadows: [
                        const Shadow(
                          color: Colors.black,
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // Selection indicator
                if (isSelected)
                  Positioned(
                    top: 6,
                    right: 6,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.all(4), // Smaller padding
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Colors.green, Colors.teal],
                        ),
                        borderRadius:
                            BorderRadius.circular(12), // Smaller radius
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.withOpacity(0.4),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.check_rounded,
                        color: Colors.white,
                        size: 14, // Smaller icon
                      ),
                    ),
                  ),

                // Rating badge
                if (movie.voteAverage != null)
                  Positioned(
                    top: 6,
                    left: 6,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6, // Smaller padding
                        vertical: 3,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.8),
                        borderRadius:
                            BorderRadius.circular(8), // Smaller radius
                        border: Border.all(
                          color: Colors.amber.withOpacity(0.5),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.star_rounded,
                            color: Colors.amber[400],
                            size: 10, // Smaller icon
                          ),
                          const SizedBox(width: 2),
                          Text(
                            movie.voteAverage!.toStringAsFixed(1),
                            style: MyStyles.title24White400.copyWith(
                              fontSize: 9, // Smaller font
                              fontWeight: FontWeight.w600,
                              color: Colors.amber[400],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // Disabled overlay
                if (!canSelect && !isSelected)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.block_rounded,
                              color: Colors.white70,
                              size: 20, // Smaller icon
                            ),
                            SizedBox(height: 4), // Smaller spacing
                            Text(
                              'Max 5',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 9, // Smaller font
                                fontWeight: FontWeight.w600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Placeholder Image Widget
  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey[800]!,
            Colors.grey[900]!,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.movie_creation_outlined,
            color: Colors.grey[600],
            size: 40,
          ),
          const SizedBox(height: 12),
          Text(
            'No Image\nAvailable',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Empty Movie Slot Widget
  Widget _buildEmptyMovieSlot() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            MyColors.secondaryColor.withOpacity(0.8),
            MyColors.secondaryColor.withOpacity(0.6),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey[600]!.withOpacity(0.5),
          width: 2,
          style: BorderStyle.solid,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(8), // Smaller padding
              decoration: BoxDecoration(
                color: Colors.grey[700]!.withOpacity(0.3),
                borderRadius: BorderRadius.circular(16), // Smaller radius
              ),
              child: Icon(
                Icons.add_rounded,
                color: Colors.grey[400],
                size: 24, // Smaller icon
              ),
            ),
            const SizedBox(height: 8), // Smaller spacing
            Text(
              'Add Movie',
              style: MyStyles.title24White400.copyWith(
                fontSize: 10, // Smaller font
                color: Colors.grey[400],
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              'Search above',
              style: MyStyles.title24White400.copyWith(
                fontSize: 8, // Smaller font
                color: Colors.grey[500],
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Search Results Widget
  Widget _buildSearchResults() {
    return BlocBuilder<SuggestSearchCubit, SuggestSearchState>(
      builder: (context, state) {
        if (state is SuggestSearchLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (state is SuggestSearchLoaded) {
          final movies = state.movies;
          if (movies.isEmpty) {
            return const Center(
              child: Text(
                'No movies found',
                style: TextStyle(color: Colors.grey),
              ),
            );
          }

          return SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: movies.length,
              itemBuilder: (context, index) {
                final movie = movies[index];
                final isSelected = _selectedMovies.contains(movie);
                return Container(
                  width: 140,
                  margin: const EdgeInsets.only(right: 12),
                  child: _buildMovieCard(movie, isSelected),
                );
              },
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  // Recommendations Grid
  Widget _buildRecommendationsGrid() {
    return FutureBuilder<List<MovieModel>>(
      future: _getRecommendations(_selectedMovies.toList()),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Text(
              'No recommendations available',
              style: TextStyle(color: Colors.grey),
            ),
          );
        }

        final recommendations = snapshot.data!;
        return SizedBox(
          height: 320, // Slightly increased height for better display
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            itemCount: recommendations.length,
            itemBuilder: (context, index) {
              final movie = recommendations[index];
              return Container(
                width: 180, // Slightly smaller width to fit 5 items better
                margin: EdgeInsets.only(
                  right: index < recommendations.length - 1 ? 16 : 0,
                ),
                child: _buildRecommendationCard(movie),
              );
            },
          ),
        );
      },
    );
  }

  // Recommendation Card
  Widget _buildRecommendationCard(MovieModel movie) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DetailsScreen(movie: movie),
          ),
        );
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Movie Poster
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                image: movie.posterPath != null
                    ? DecorationImage(
                        image: NetworkImage(
                          '$kmoviedbImageURL${movie.posterPath}',
                        ),
                        fit: BoxFit.cover,
                      )
                    : null,
                color:
                    movie.posterPath == null ? MyColors.secondaryColor : null,
              ),
              child: Stack(
                children: [
                  // Rating Badge
                  if (movie.voteAverage != null)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 12,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              movie.voteAverage!.toStringAsFixed(1),
                              style: MyStyles.title24White400.copyWith(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          vSpace(8),
          // Movie Info
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Country and Year
              Text(
                'USA, ${movie.releaseDate?.substring(0, 4) ?? '2024'}',
                style: MyStyles.title24White400.copyWith(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
              vSpace(4),
              // Title
              Text(
                movie.title ?? 'Unknown',
                style: MyStyles.title24White400.copyWith(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              vSpace(4),
              // Genre
              Text(
                'Action, Adventure',
                style: MyStyles.title24White400.copyWith(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to get smart recommendations
  Future<List<MovieModel>> _getRecommendations(
      List<MovieModel> selectedMovies) async {
    if (selectedMovies.isEmpty) {
      return [];
    }

    try {
      final apiService = ApiService();
      final Set<MovieModel> allCandidates = {};

      // تحليل الأفلام المختارة لفهم تفضيلات المستخدم
      final userPreferences = _analyzeUserPreferences(selectedMovies);

      print('🎯 تحليل تفضيلات المستخدم:');
      print(
          '📽️ الأفلام المختارة: ${selectedMovies.map((m) => m.title).join(", ")}');
      print('🎭 الأنواع المفضلة: ${userPreferences['preferredGenres']}');
      print(
          '⭐ متوسط التقييم: ${userPreferences['averageRating'].toStringAsFixed(1)}');
      print(
          '📅 نطاق السنوات: ${userPreferences['yearRange']['min']}-${userPreferences['yearRange']['max']}');

      // الحصول على أفلام مشابهة لكل فيلم مختار
      for (final movie in selectedMovies) {
        if (movie.id != null) {
          try {
            print('🔍 البحث عن أفلام مشابهة لـ: ${movie.title}');
            final similarMovies = await apiService.getMovieData(
              MovieType.similar,
              movieID: movie.id!,
            );
            allCandidates.addAll(similarMovies);
            print('✅ تم العثور على ${similarMovies.length} فيلم مشابه');
          } catch (e) {
            print('❌ خطأ في الحصول على أفلام مشابهة لـ ${movie.title}: $e');
          }
        }
      }

      // إضافة أفلام من مصادر متعددة لضمان التنوع
      await _addMoviesFromMultipleSources(
          apiService, allCandidates, userPreferences);

      // إزالة الأفلام المختارة والمكررة
      final selectedIds = selectedMovies.map((m) => m.id).toSet();
      final uniqueCandidates = <int, MovieModel>{};

      // إزالة المكررات باستخدام ID كمفتاح
      for (final movie in allCandidates) {
        if (movie.id != null && !selectedIds.contains(movie.id)) {
          uniqueCandidates[movie.id!] = movie;
        }
      }

      final filteredCandidates = uniqueCandidates.values.toList();

      print('🎬 إجمالي الأفلام المرشحة: ${filteredCandidates.length}');

      // ضمان الحصول على 5 توصيات بالضبط
      final finalRecommendations = await _ensureExactly5Recommendations(
        filteredCandidates,
        selectedMovies,
        userPreferences,
        apiService,
      );

      print(
          '🌟 التوصيات النهائية (${finalRecommendations.length}): ${finalRecommendations.map((m) => m.title).join(", ")}');

      return finalRecommendations;
    } catch (e) {
      print('❌ خطأ في الحصول على التوصيات: $e');
      return [];
    }
  }

  // تحليل تفضيلات المستخدم
  Map<String, dynamic> _analyzeUserPreferences(
      List<MovieModel> selectedMovies) {
    // تحليل الأنواع المفضلة
    final Map<int, int> genreCount = {};
    final List<double> ratings = [];
    final List<int> years = [];
    final Set<String> keywords = {};

    for (final movie in selectedMovies) {
      // تحليل النوعية
      if (movie.genreIds != null) {
        for (final genreId in movie.genreIds!) {
          genreCount[genreId] = (genreCount[genreId] ?? 0) + 1;
        }
      }

      // تحليل التقييمات
      if (movie.voteAverage != null && movie.voteAverage! > 0) {
        ratings.add(movie.voteAverage!);
      }

      // تحليل السنوات
      if (movie.releaseDate != null && movie.releaseDate!.isNotEmpty) {
        try {
          final year = int.parse(movie.releaseDate!.substring(0, 4));
          years.add(year);
        } catch (e) {
          // تجاهل الأخطاء في تحليل التاريخ
        }
      }

      // تحليل الكلمات المفتاحية من العنوان
      if (movie.title != null) {
        final titleWords = movie.title!.toLowerCase().split(' ');
        for (final word in titleWords) {
          if (word.length > 3) {
            // تجاهل الكلمات القصيرة
            keywords.add(word);
          }
        }
      }
    }

    // حساب الأنواع المفضلة
    final preferredGenres = genreCount.entries
        .where((entry) =>
            entry.value >= 2) // النوعية التي تظهر في فيلمين على الأقل
        .map((entry) => entry.key)
        .toList();

    // حساب متوسط التقييم
    final averageRating = ratings.isNotEmpty
        ? ratings.reduce((a, b) => a + b) / ratings.length
        : 7.0;

    // حساب نطاق السنوات
    final minYear =
        years.isNotEmpty ? years.reduce((a, b) => a < b ? a : b) : 2020;
    final maxYear =
        years.isNotEmpty ? years.reduce((a, b) => a > b ? a : b) : 2024;

    return {
      'preferredGenres': preferredGenres,
      'averageRating': averageRating,
      'ratingRange': [averageRating - 1.5, averageRating + 1.5],
      'yearRange': {'min': minYear - 5, 'max': maxYear + 2},
      'keywords': keywords.toList(),
    };
  }

  // إضافة أفلام من نفس النوعية
  Future<void> _addMoviesByGenre(
    ApiService apiService,
    Set<MovieModel> allCandidates,
    Map<String, dynamic> userPreferences,
  ) async {
    final preferredGenres = userPreferences['preferredGenres'] as List<int>;

    if (preferredGenres.isNotEmpty) {
      try {
        // الحصول على أفلام شائعة من نفس النوعية
        final popularMovies = await apiService.getMovieData(MovieType.popular);
        final topRatedMovies =
            await apiService.getMovieData(MovieType.topRated);

        // تصفية الأفلام حسب النوعية المفضلة
        final genreFilteredMovies =
            [...popularMovies, ...topRatedMovies].where((movie) {
          if (movie.genreIds == null) return false;
          return movie.genreIds!
              .any((genreId) => preferredGenres.contains(genreId));
        }).toList();

        allCandidates.addAll(genreFilteredMovies);
        print(
            '🎭 تم إضافة ${genreFilteredMovies.length} فيلم من الأنواع المفضلة');
      } catch (e) {
        print('❌ خطأ في إضافة أفلام من نفس النوعية: $e');
      }
    }
  }

  // تطبيق خوارزمية التوصية الذكية
  List<MovieModel> _applySmartRecommendationAlgorithm(
    List<MovieModel> candidates,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
  ) {
    final List<MapEntry<MovieModel, double>> scoredMovies = [];
    final Set<int> processedIds = {};

    // حساب النقاط مع تجنب المكررات
    for (final movie in candidates) {
      if (movie.id != null && !processedIds.contains(movie.id)) {
        final score =
            _calculateMovieScore(movie, selectedMovies, userPreferences);
        scoredMovies.add(MapEntry(movie, score));
        processedIds.add(movie.id!);
      }
    }

    // ترتيب الأفلام حسب النقاط (من الأعلى للأقل)
    scoredMovies.sort((a, b) => b.value.compareTo(a.value));

    // فلترة إضافية لضمان التنوع
    final diverseRecommendations =
        _ensureDiverseRecommendations(scoredMovies, userPreferences);

    // طباعة أفضل 10 نتائج للتحليل
    print('🏆 أفضل 10 أفلام مرشحة (بعد ضمان التنوع):');
    for (int i = 0; i < 10 && i < diverseRecommendations.length; i++) {
      final entry = diverseRecommendations[i];
      print(
          '${i + 1}. ${entry.key.title} - النقاط: ${entry.value.toStringAsFixed(2)}');
    }

    return diverseRecommendations.map((entry) => entry.key).toList();
  }

  // ضمان التنوع في التوصيات
  List<MapEntry<MovieModel, double>> _ensureDiverseRecommendations(
    List<MapEntry<MovieModel, double>> scoredMovies,
    Map<String, dynamic> userPreferences,
  ) {
    final List<MapEntry<MovieModel, double>> diverseList = [];
    final Set<String> usedTitles = {};
    final Map<int, int> genreCount = {};
    final Set<int> usedYears = {};

    for (final entry in scoredMovies) {
      final movie = entry.key;
      bool shouldAdd = true;

      // تجنب الأفلام بنفس الاسم (أو أسماء متشابهة جداً)
      if (movie.title != null) {
        final normalizedTitle = _normalizeTitle(movie.title!);
        if (usedTitles.contains(normalizedTitle)) {
          shouldAdd = false;
        } else {
          usedTitles.add(normalizedTitle);
        }
      }

      // ضمان التنوع في الأنواع (لا أكثر من 3 أفلام من نفس النوع)
      if (shouldAdd && movie.genreIds != null) {
        bool genreOverused = false;
        for (final genreId in movie.genreIds!) {
          final currentCount = genreCount[genreId] ?? 0;
          if (currentCount >= 3) {
            genreOverused = true;
            break;
          }
        }
        if (genreOverused) {
          shouldAdd = false;
        } else {
          // تحديث عداد الأنواع
          for (final genreId in movie.genreIds!) {
            genreCount[genreId] = (genreCount[genreId] ?? 0) + 1;
          }
        }
      }

      // ضمان التنوع في السنوات (لا أكثر من فيلمين من نفس السنة)
      if (shouldAdd &&
          movie.releaseDate != null &&
          movie.releaseDate!.isNotEmpty) {
        try {
          final year = int.parse(movie.releaseDate!.substring(0, 4));
          if (usedYears.contains(year) && diverseList.length >= 2) {
            // السماح بفيلمين فقط من نفس السنة
            final sameYearCount = diverseList.where((item) {
              if (item.key.releaseDate == null ||
                  item.key.releaseDate!.isEmpty) {
                return false;
              }
              try {
                final itemYear =
                    int.parse(item.key.releaseDate!.substring(0, 4));
                return itemYear == year;
              } catch (e) {
                return false;
              }
            }).length;

            if (sameYearCount >= 2) {
              shouldAdd = false;
            }
          }
          if (shouldAdd) {
            usedYears.add(year);
          }
        } catch (e) {
          // تجاهل أخطاء تحليل التاريخ
        }
      }

      if (shouldAdd) {
        diverseList.add(entry);

        // التوقف عند الوصول لـ 10 توصيات متنوعة
        if (diverseList.length >= 10) {
          break;
        }
      }
    }

    return diverseList;
  }

  // تطبيع عنوان الفيلم لمقارنة أفضل
  String _normalizeTitle(String title) {
    return title
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '') // إزالة علامات الترقيم
        .replaceAll(RegExp(r'\s+'), ' ') // توحيد المسافات
        .trim();
  }

  // حساب نقاط الفيلم بناءً على التشابه المحسن
  double _calculateMovieScore(
    MovieModel movie,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
  ) {
    double score = 0.0;

    // 1. نقاط التشابه في الاسم/الكلمات المفتاحية (40% من النقاط) - الأولوية العليا
    score +=
        _calculateAdvancedTitleScore(movie, selectedMovies, userPreferences) *
            0.40;

    // 2. نقاط التشابه المباشر مع الأفلام المختارة (20% من النقاط)
    score += _calculateDirectSimilarityScore(movie, selectedMovies) * 0.20;

    // 3. نقاط التشابه في النوعية (15% من النقاط) - تقليل كبير
    score +=
        _calculateAdvancedGenreScore(movie, selectedMovies, userPreferences) *
            0.15;

    // 4. نقاط التشابه في التقييم (10% من النقاط)
    score += _calculateAdvancedRatingScore(movie, userPreferences) * 0.10;

    // 5. نقاط التشابه في السنة (10% من النقاط)
    score += _calculateAdvancedYearScore(movie, userPreferences) * 0.10;

    // 6. نقاط الشعبية والجودة (5% من النقاط)
    score += _calculateAdvancedPopularityScore(movie) * 0.05;

    return score;
  }

  // حساب نقاط التشابه في النوعية
  double _calculateGenreScore(
      MovieModel movie, Map<String, dynamic> userPreferences) {
    final preferredGenres = userPreferences['preferredGenres'] as List<int>;
    if (preferredGenres.isEmpty || movie.genreIds == null) return 0.0;

    final matchingGenres = movie.genreIds!
        .where((genreId) => preferredGenres.contains(genreId))
        .length;

    // نقاط أعلى للأفلام التي تشارك أكثر من نوعية
    return (matchingGenres / preferredGenres.length) * 100;
  }

  // حساب نقاط التشابه في التقييم
  double _calculateRatingScore(
      MovieModel movie, Map<String, dynamic> userPreferences) {
    if (movie.voteAverage == null) return 0.0;

    final averageRating = userPreferences['averageRating'] as double;
    final ratingDifference = (movie.voteAverage! - averageRating).abs();

    // نقاط أعلى للأفلام القريبة من متوسط التقييم المفضل
    if (ratingDifference <= 0.5) return 100.0;
    if (ratingDifference <= 1.0) return 80.0;
    if (ratingDifference <= 1.5) return 60.0;
    if (ratingDifference <= 2.0) return 40.0;
    return 20.0;
  }

  // حساب نقاط التشابه في السنة
  double _calculateYearScore(
      MovieModel movie, Map<String, dynamic> userPreferences) {
    if (movie.releaseDate == null || movie.releaseDate!.isEmpty) return 50.0;

    try {
      final movieYear = int.parse(movie.releaseDate!.substring(0, 4));
      final yearRange = userPreferences['yearRange'] as Map<String, dynamic>;
      final minYear = yearRange['min'] as int;
      final maxYear = yearRange['max'] as int;

      if (movieYear >= minYear && movieYear <= maxYear) {
        return 100.0; // في النطاق المفضل
      } else {
        // نقاط أقل كلما ابتعد عن النطاق
        final distance =
            movieYear < minYear ? minYear - movieYear : movieYear - maxYear;
        return (100.0 - (distance * 5)).clamp(0.0, 100.0);
      }
    } catch (e) {
      return 50.0; // نقاط متوسطة في حالة الخطأ
    }
  }

  // حساب نقاط التشابه في الاسم
  double _calculateTitleScore(
      MovieModel movie, Map<String, dynamic> userPreferences) {
    if (movie.title == null) return 0.0;

    final keywords = userPreferences['keywords'] as List<String>;
    if (keywords.isEmpty) return 50.0;

    final movieTitle = movie.title!.toLowerCase();
    int matchingKeywords = 0;

    for (final keyword in keywords) {
      if (movieTitle.contains(keyword)) {
        matchingKeywords++;
      }
    }

    return (matchingKeywords / keywords.length) * 100;
  }

  // حساب نقاط الشعبية والجودة
  double _calculatePopularityScore(MovieModel movie) {
    double score = 0.0;

    // نقاط التقييم
    if (movie.voteAverage != null) {
      score += (movie.voteAverage! / 10) * 50; // حتى 50 نقطة
    }

    // نقاط الشعبية
    if (movie.popularity != null) {
      // تطبيع الشعبية (عادة تكون بين 0-100)
      final normalizedPopularity = (movie.popularity! / 100).clamp(0.0, 1.0);
      score += normalizedPopularity * 50; // حتى 50 نقطة
    }

    return score;
  }

  // ===== الدوال المحسنة للتوصيات =====

  // حساب نقاط النوعية المحسن
  double _calculateAdvancedGenreScore(
    MovieModel movie,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
  ) {
    if (movie.genreIds == null || movie.genreIds!.isEmpty) return 0.0;

    final preferredGenres = userPreferences['preferredGenres'] as List<int>;
    if (preferredGenres.isEmpty) return 50.0;

    double score = 0.0;

    // 1. نقاط التطابق المباشر في النوعية
    final matchingGenres = movie.genreIds!
        .where((genreId) => preferredGenres.contains(genreId))
        .length;
    score += (matchingGenres / preferredGenres.length) * 60;

    // 2. نقاط إضافية للأنواع الشائعة في الأفلام المختارة
    final Map<int, int> selectedGenreFrequency = {};
    for (final selectedMovie in selectedMovies) {
      if (selectedMovie.genreIds != null) {
        for (final genreId in selectedMovie.genreIds!) {
          selectedGenreFrequency[genreId] =
              (selectedGenreFrequency[genreId] ?? 0) + 1;
        }
      }
    }

    for (final genreId in movie.genreIds!) {
      final frequency = selectedGenreFrequency[genreId] ?? 0;
      if (frequency > 0) {
        score += (frequency / selectedMovies.length) * 40;
      }
    }

    return score.clamp(0.0, 100.0);
  }

  // حساب نقاط التقييم المحسن
  double _calculateAdvancedRatingScore(
    MovieModel movie,
    Map<String, dynamic> userPreferences,
  ) {
    if (movie.voteAverage == null) return 30.0;

    final averageRating = userPreferences['averageRating'] as double;
    final ratingDifference = (movie.voteAverage! - averageRating).abs();

    // منحنى تقييم أكثر سلاسة
    if (ratingDifference <= 0.3) return 100.0;
    if (ratingDifference <= 0.6) return 95.0;
    if (ratingDifference <= 1.0) return 85.0;
    if (ratingDifference <= 1.5) return 70.0;
    if (ratingDifference <= 2.0) return 50.0;
    if (ratingDifference <= 2.5) return 30.0;
    return 10.0;
  }

  // حساب نقاط السنة المحسن
  double _calculateAdvancedYearScore(
    MovieModel movie,
    Map<String, dynamic> userPreferences,
  ) {
    if (movie.releaseDate == null || movie.releaseDate!.isEmpty) return 40.0;

    try {
      final movieYear = int.parse(movie.releaseDate!.substring(0, 4));
      final yearRange = userPreferences['yearRange'] as Map<String, dynamic>;
      final minYear = yearRange['min'] as int;
      final maxYear = yearRange['max'] as int;
      final centerYear = (minYear + maxYear) / 2;

      // نقاط أعلى للأفلام القريبة من مركز النطاق المفضل
      if (movieYear >= minYear && movieYear <= maxYear) {
        final distanceFromCenter = (movieYear - centerYear).abs();
        final maxDistance = (maxYear - minYear) / 2;
        return 100.0 - (distanceFromCenter / maxDistance * 20);
      } else {
        // نقاط متدرجة للأفلام خارج النطاق
        final distance =
            movieYear < minYear ? minYear - movieYear : movieYear - maxYear;
        return (80.0 - (distance * 3)).clamp(0.0, 80.0);
      }
    } catch (e) {
      return 40.0;
    }
  }

  // حساب نقاط العنوان المحسن والمطور
  double _calculateAdvancedTitleScore(
    MovieModel movie,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
  ) {
    if (movie.title == null) return 0.0;

    double score = 0.0;
    final movieTitle = movie.title!.toLowerCase();

    // 1. التشابه المباشر مع عناوين الأفلام المختارة (50% من نقاط العنوان) - الأولوية العليا
    double titleSimilarityScore = 0.0;
    for (final selectedMovie in selectedMovies) {
      if (selectedMovie.title != null) {
        final selectedTitle = selectedMovie.title!.toLowerCase();
        final similarity =
            _calculateEnhancedStringSimilarity(movieTitle, selectedTitle);
        titleSimilarityScore += similarity;
      }
    }
    if (selectedMovies.isNotEmpty) {
      score += (titleSimilarityScore / selectedMovies.length) * 50;
    }

    // 2. تحليل السلاسل والامتدادات (25% من نقاط العنوان) - زيادة كبيرة
    score += _calculateSeriesAndSequelScore(movieTitle, selectedMovies) * 25;

    // 3. التشابه مع الكلمات المفتاحية المستخرجة (15% من نقاط العنوان)
    final keywords = userPreferences['keywords'] as List<String>;
    if (keywords.isNotEmpty) {
      double keywordScore = 0.0;
      for (final keyword in keywords) {
        if (movieTitle.contains(keyword)) {
          // نقاط إضافية للكلمات الأطول والأكثر تحديداً
          final keywordWeight = keyword.length > 4 ? 3.0 : 1.5; // زيادة الأوزان
          keywordScore += keywordWeight;
        }
      }
      score += (keywordScore / keywords.length) * 15;
    }

    // 4. تحليل الكلمات المشتركة المهمة (10% من نقاط العنوان)
    score += _calculateImportantWordsScore(movieTitle, selectedMovies) * 10;

    return score.clamp(0.0, 100.0);
  }

  // حساب التشابه المحسن بين النصوص
  double _calculateEnhancedStringSimilarity(String str1, String str2) {
    if (str1 == str2) return 100.0;

    // تطبيع النصوص
    final normalizedStr1 = _normalizeMovieTitle(str1);
    final normalizedStr2 = _normalizeMovieTitle(str2);

    if (normalizedStr1 == normalizedStr2) return 95.0;

    // تحليل الكلمات
    final words1 = normalizedStr1.split(' ').where((w) => w.length > 2).toSet();
    final words2 = normalizedStr2.split(' ').where((w) => w.length > 2).toSet();

    if (words1.isEmpty && words2.isEmpty) return 100.0;
    if (words1.isEmpty || words2.isEmpty) return 0.0;

    // حساب Jaccard similarity
    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;
    final jaccardSimilarity = intersection / union;

    // حساب نسبة الكلمات المشتركة
    final commonWordsRatio =
        intersection / words1.length.clamp(1, double.infinity);

    // حساب التشابه في بداية العنوان (مهم للسلاسل)
    final startSimilarity =
        _calculateStartSimilarity(normalizedStr1, normalizedStr2);

    // حساب التشابه الصوتي (للأسماء المتشابهة صوتياً)
    final phoneticSimilarity =
        _calculatePhoneticSimilarity(normalizedStr1, normalizedStr2);

    // حساب التشابه في الطول (الأسماء المتشابهة غالباً لها أطوال متقاربة)
    final lengthSimilarity =
        _calculateLengthSimilarity(normalizedStr1, normalizedStr2);

    // دمج النتائج مع أوزان محسنة لصالح التشابه في الأسماء
    final finalScore = (jaccardSimilarity * 40) + // تقليل قليل
        (commonWordsRatio * 25) + // تقليل قليل
        (startSimilarity * 20) + // نفس القيمة
        (phoneticSimilarity * 10) + // جديد
        (lengthSimilarity * 5); // جديد

    return finalScore.clamp(0.0, 100.0);
  }

  // تطبيع عنوان الفيلم للمقارنة
  String _normalizeMovieTitle(String title) {
    return title
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), ' ') // استبدال علامات الترقيم بمسافات
        .replaceAll(RegExp(r'\b(the|a|an|and|or|of|in|on|at|to|for|with|by)\b'),
            '') // إزالة كلمات الربط
        .replaceAll(RegExp(r'\s+'), ' ') // توحيد المسافات
        .trim();
  }

  // حساب التشابه في بداية العنوان
  double _calculateStartSimilarity(String str1, String str2) {
    final minLength =
        [str1.length, str2.length, 10].reduce((a, b) => a < b ? a : b);
    if (minLength == 0) return 0.0;

    int matchingChars = 0;
    for (int i = 0; i < minLength; i++) {
      if (str1[i] == str2[i]) {
        matchingChars++;
      } else {
        break;
      }
    }

    return (matchingChars / minLength) * 100;
  }

  // حساب التشابه الصوتي البسيط
  double _calculatePhoneticSimilarity(String str1, String str2) {
    // تحويل إلى أحرف صوتية مبسطة
    final phonetic1 = _toSimplePhonetic(str1);
    final phonetic2 = _toSimplePhonetic(str2);

    if (phonetic1 == phonetic2) return 100.0;
    if (phonetic1.isEmpty || phonetic2.isEmpty) return 0.0;

    // حساب التشابه في الأحرف الصوتية
    final minLength =
        [phonetic1.length, phonetic2.length].reduce((a, b) => a < b ? a : b);
    int matchingChars = 0;

    for (int i = 0; i < minLength; i++) {
      if (phonetic1[i] == phonetic2[i]) {
        matchingChars++;
      }
    }

    return (matchingChars / phonetic1.length.clamp(1, double.infinity)) * 100;
  }

  // تحويل النص إلى تمثيل صوتي مبسط
  String _toSimplePhonetic(String text) {
    return text
        .toLowerCase()
        .replaceAll(RegExp(r'[aeiou]'), '') // إزالة الحروف المتحركة
        .replaceAll(RegExp(r'[^a-z]'), '') // الاحتفاظ بالأحرف الساكنة فقط
        .replaceAll(RegExp(r'(.)\1+'), r'$1'); // إزالة التكرار
  }

  // حساب التشابه في الطول
  double _calculateLengthSimilarity(String str1, String str2) {
    if (str1.isEmpty && str2.isEmpty) return 100.0;
    if (str1.isEmpty || str2.isEmpty) return 0.0;

    final length1 = str1.length;
    final length2 = str2.length;
    final maxLength = [length1, length2].reduce((a, b) => a > b ? a : b);
    final minLength = [length1, length2].reduce((a, b) => a < b ? a : b);

    // كلما كان الفرق أقل، كان التشابه أعلى
    final similarity = (minLength / maxLength) * 100;
    return similarity;
  }

  // حساب نقاط السلاسل والامتدادات
  double _calculateSeriesAndSequelScore(
      String movieTitle, List<MovieModel> selectedMovies) {
    double score = 0.0;

    // كلمات تدل على السلاسل والامتدادات
    final seriesKeywords = [
      'part',
      'episode',
      'chapter',
      'volume',
      'season',
      'ii',
      'iii',
      'iv',
      'v',
      'vi',
      'vii',
      'viii',
      'ix',
      'x',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      'two',
      'three',
      'four',
      'five',
      'six',
      'seven',
      'eight',
      'nine',
      'ten',
      'sequel',
      'prequel',
      'returns',
      'revenge',
      'rise',
      'dawn',
      'war',
      'begins',
      'origins',
      'legacy',
      'reborn',
      'resurrection'
    ];

    for (final selectedMovie in selectedMovies) {
      if (selectedMovie.title != null) {
        final selectedTitle = selectedMovie.title!.toLowerCase();

        // البحث عن كلمات السلاسل
        bool hasSeriesIndicator = false;
        for (final keyword in seriesKeywords) {
          if (movieTitle.contains(keyword) || selectedTitle.contains(keyword)) {
            hasSeriesIndicator = true;
            break;
          }
        }

        if (hasSeriesIndicator) {
          // إذا كان هناك مؤشر على السلسلة، ابحث عن الجزء الأساسي من الاسم
          final baseTitle1 = _extractBaseTitleFromSeries(movieTitle);
          final baseTitle2 = _extractBaseTitleFromSeries(selectedTitle);

          if (baseTitle1.isNotEmpty && baseTitle2.isNotEmpty) {
            final baseSimilarity =
                _calculateEnhancedStringSimilarity(baseTitle1, baseTitle2);
            score += baseSimilarity;
          }
        }
      }
    }

    return selectedMovies.isNotEmpty ? score / selectedMovies.length : 0.0;
  }

  // استخراج العنوان الأساسي من السلسلة
  String _extractBaseTitleFromSeries(String title) {
    // إزالة الأرقام والكلمات الدالة على التسلسل من نهاية العنوان
    final cleanTitle = title
        .replaceAll(
            RegExp(r'\b(part|episode|chapter|volume|season)\s*\d+\b'), '')
        .replaceAll(RegExp(r'\b(ii|iii|iv|v|vi|vii|viii|ix|x)\b'), '')
        .replaceAll(RegExp(r'\b\d+\b'), '')
        .replaceAll(
            RegExp(r'\b(two|three|four|five|six|seven|eight|nine|ten)\b'), '')
        .replaceAll(
            RegExp(
                r'\b(sequel|prequel|returns|revenge|rise|dawn|war|begins|origins|legacy|reborn|resurrection)\b'),
            '')
        .replaceAll(
            RegExp(r'[:\-–—].*$'), '') // إزالة كل شيء بعد النقطتين أو الشرطة
        .trim();

    return cleanTitle;
  }

  // حساب نقاط الكلمات المهمة المشتركة
  double _calculateImportantWordsScore(
      String movieTitle, List<MovieModel> selectedMovies) {
    double score = 0.0;

    // كلمات مهمة تدل على نوع الفيلم أو الموضوع
    final importantWords = _extractImportantWords(movieTitle);

    if (importantWords.isEmpty) return 0.0;

    for (final selectedMovie in selectedMovies) {
      if (selectedMovie.title != null) {
        final selectedImportantWords =
            _extractImportantWords(selectedMovie.title!.toLowerCase());

        final commonImportantWords =
            importantWords.intersection(selectedImportantWords).length;
        if (commonImportantWords > 0) {
          score += (commonImportantWords / importantWords.length) * 100;
        }
      }
    }

    return selectedMovies.isNotEmpty ? score / selectedMovies.length : 0.0;
  }

  // استخراج الكلمات المهمة من العنوان
  Set<String> _extractImportantWords(String title) {
    final words = title.toLowerCase().split(' ');
    final importantWords = <String>{};

    // كلمات مهمة تدل على الموضوع أو النوع
    final significantWords = [
      'war',
      'battle',
      'fight',
      'hero',
      'super',
      'man',
      'woman',
      'king',
      'queen',
      'dark',
      'light',
      'black',
      'white',
      'red',
      'blue',
      'green',
      'gold',
      'silver',
      'dragon',
      'knight',
      'warrior',
      'soldier',
      'captain',
      'commander',
      'general',
      'magic',
      'wizard',
      'witch',
      'spell',
      'curse',
      'power',
      'force',
      'energy',
      'space',
      'star',
      'galaxy',
      'planet',
      'earth',
      'world',
      'universe',
      'cosmic',
      'time',
      'future',
      'past',
      'ancient',
      'modern',
      'new',
      'old',
      'young',
      'love',
      'heart',
      'soul',
      'spirit',
      'mind',
      'dream',
      'nightmare',
      'hope',
      'death',
      'life',
      'blood',
      'fire',
      'water',
      'ice',
      'storm',
      'thunder',
      'secret',
      'mystery',
      'hidden',
      'lost',
      'found',
      'treasure',
      'quest',
      'journey'
    ];

    for (final word in words) {
      if (word.length > 4 || significantWords.contains(word)) {
        importantWords.add(word);
      }
    }

    return importantWords;
  }

  // حساب نقاط الشعبية المحسن
  double _calculateAdvancedPopularityScore(MovieModel movie) {
    double score = 0.0;

    // 1. نقاط التقييم مع منحنى محسن
    if (movie.voteAverage != null) {
      if (movie.voteAverage! >= 8.0) {
        score += 50.0;
      } else if (movie.voteAverage! >= 7.0) {
        score += 40.0;
      } else if (movie.voteAverage! >= 6.0) {
        score += 30.0;
      } else if (movie.voteAverage! >= 5.0) {
        score += 20.0;
      } else {
        score += 10.0;
      }
    }

    // 2. نقاط الشعبية مع تطبيع أفضل
    if (movie.popularity != null) {
      if (movie.popularity! >= 50) {
        score += 30.0;
      } else if (movie.popularity! >= 20) {
        score += 25.0;
      } else if (movie.popularity! >= 10) {
        score += 20.0;
      } else if (movie.popularity! >= 5) {
        score += 15.0;
      } else {
        score += 10.0;
      }
    }

    // 3. نقاط إضافية لعدد الأصوات
    if (movie.voteCount != null) {
      if (movie.voteCount! >= 5000) {
        score += 20.0;
      } else if (movie.voteCount! >= 1000) {
        score += 15.0;
      } else if (movie.voteCount! >= 500) {
        score += 10.0;
      } else if (movie.voteCount! >= 100) {
        score += 5.0;
      }
    }

    return score.clamp(0.0, 100.0);
  }

  // حساب التشابه المباشر مع الأفلام المختارة
  double _calculateDirectSimilarityScore(
    MovieModel movie,
    List<MovieModel> selectedMovies,
  ) {
    double totalSimilarity = 0.0;
    int comparisons = 0;

    for (final selectedMovie in selectedMovies) {
      double similarity = 0.0;

      // 1. تشابه النوعية
      if (movie.genreIds != null && selectedMovie.genreIds != null) {
        final commonGenres = movie.genreIds!
            .where((id) => selectedMovie.genreIds!.contains(id))
            .length;
        final totalGenres =
            (movie.genreIds!.length + selectedMovie.genreIds!.length) / 2;
        similarity += (commonGenres / totalGenres) * 40;
      }

      // 2. تشابه التقييم
      if (movie.voteAverage != null && selectedMovie.voteAverage != null) {
        final ratingDiff =
            (movie.voteAverage! - selectedMovie.voteAverage!).abs();
        similarity += (2.0 - ratingDiff.clamp(0.0, 2.0)) / 2.0 * 30;
      }

      // 3. تشابه السنة
      if (movie.releaseDate != null && selectedMovie.releaseDate != null) {
        try {
          final movieYear = int.parse(movie.releaseDate!.substring(0, 4));
          final selectedYear =
              int.parse(selectedMovie.releaseDate!.substring(0, 4));
          final yearDiff = (movieYear - selectedYear).abs();
          similarity += (10.0 - yearDiff.clamp(0.0, 10.0)) / 10.0 * 30;
        } catch (e) {
          // تجاهل أخطاء تحليل التاريخ
        }
      }

      totalSimilarity += similarity;
      comparisons++;
    }

    return comparisons > 0 ? totalSimilarity / comparisons : 0.0;
  }

  // حساب التشابه بين النصوص
  double _calculateStringSimilarity(String str1, String str2) {
    if (str1 == str2) return 1.0;

    final words1 = str1.split(' ').where((w) => w.length > 2).toSet();
    final words2 = str2.split(' ').where((w) => w.length > 2).toSet();

    if (words1.isEmpty && words2.isEmpty) return 1.0;
    if (words1.isEmpty || words2.isEmpty) return 0.0;

    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;

    return intersection / union;
  }

  // ===== دوال محسنة لضمان 5 توصيات =====

  // إضافة أفلام من مصادر متعددة
  Future<void> _addMoviesFromMultipleSources(
    ApiService apiService,
    Set<MovieModel> allCandidates,
    Map<String, dynamic> userPreferences,
  ) async {
    try {
      // 1. أفلام من نفس النوعية
      await _addMoviesByGenre(apiService, allCandidates, userPreferences);

      // 2. أفلام شائعة حديثة
      final nowPlayingMovies =
          await apiService.getMovieData(MovieType.nowPlaying);
      allCandidates.addAll(nowPlayingMovies);
      print('🎬 تم إضافة ${nowPlayingMovies.length} فيلم من الأفلام الحالية');

      // 3. أفلام قادمة
      final upcomingMovies = await apiService.getMovieData(MovieType.upcoming);
      allCandidates.addAll(upcomingMovies);
      print('🔮 تم إضافة ${upcomingMovies.length} فيلم من الأفلام القادمة');

      // 4. أفلام إضافية من الأعلى تقييماً
      final topRatedMovies = await apiService.getMovieData(MovieType.topRated);
      allCandidates.addAll(topRatedMovies);
      print('⭐ تم إضافة ${topRatedMovies.length} فيلم من الأعلى تقييماً');
    } catch (e) {
      print('❌ خطأ في إضافة أفلام من مصادر متعددة: $e');
    }
  }

  // ضمان الحصول على 5 توصيات بالضبط
  Future<List<MovieModel>> _ensureExactly5Recommendations(
    List<MovieModel> candidates,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
    ApiService apiService,
  ) async {
    print('🎯 بدء عملية ضمان 5 توصيات من ${candidates.length} مرشح');

    // المرحلة 1: تطبيق الخوارزمية الذكية
    List<MovieModel> recommendations = _applySmartRecommendationAlgorithm(
      candidates,
      selectedMovies,
      userPreferences,
    );

    print('📊 المرحلة 1: حصلنا على ${recommendations.length} توصية');

    // المرحلة 2: إذا كان لدينا أقل من 5، نضيف المزيد
    if (recommendations.length < 5) {
      print('⚠️ نحتاج المزيد من التوصيات، البحث عن أفلام إضافية...');

      final additionalMovies = await _getAdditionalMovies(
        apiService,
        selectedMovies,
        userPreferences,
        recommendations,
        5 - recommendations.length,
      );

      recommendations.addAll(additionalMovies);
      print('➕ تم إضافة ${additionalMovies.length} فيلم إضافي');
    }

    // المرحلة 3: إذا كان لدينا أكثر من 5، نختار الأفضل
    if (recommendations.length > 5) {
      recommendations = recommendations.take(5).toList();
      print('✂️ تم تقليم القائمة إلى 5 أفلام');
    }

    // المرحلة 4: التحقق النهائي وإضافة أفلام احتياطية إذا لزم الأمر
    if (recommendations.length < 5) {
      print(
          '🚨 ما زلنا نحتاج ${5 - recommendations.length} أفلام، إضافة أفلام احتياطية...');

      final backupMovies = await _getBackupMovies(
        apiService,
        recommendations,
        5 - recommendations.length,
      );

      recommendations.addAll(backupMovies);
      print('🔄 تم إضافة ${backupMovies.length} فيلم احتياطي');
    }

    // ضمان عدم تجاوز 5 أفلام
    final finalList = recommendations.take(5).toList();

    print('✅ النتيجة النهائية: ${finalList.length} أفلام');
    for (int i = 0; i < finalList.length; i++) {
      print('${i + 1}. ${finalList[i].title}');
    }

    return finalList;
  }

  // الحصول على أفلام إضافية
  Future<List<MovieModel>> _getAdditionalMovies(
    ApiService apiService,
    List<MovieModel> selectedMovies,
    Map<String, dynamic> userPreferences,
    List<MovieModel> existingRecommendations,
    int needed,
  ) async {
    final List<MovieModel> additionalMovies = [];
    final existingIds = existingRecommendations.map((m) => m.id).toSet();
    final selectedIds = selectedMovies.map((m) => m.id).toSet();

    try {
      // البحث في الأفلام الشائعة
      final popularMovies = await apiService.getMovieData(MovieType.popular);

      for (final movie in popularMovies) {
        if (additionalMovies.length >= needed) break;

        if (movie.id != null &&
            !existingIds.contains(movie.id) &&
            !selectedIds.contains(movie.id)) {
          // تحقق من التوافق مع التفضيلات
          if (_isMovieCompatible(movie, userPreferences)) {
            additionalMovies.add(movie);
            existingIds.add(movie.id!);
          }
        }
      }

      // إذا ما زلنا نحتاج المزيد، ابحث في الأفلام الأعلى تقييماً
      if (additionalMovies.length < needed) {
        final topRatedMovies =
            await apiService.getMovieData(MovieType.topRated);

        for (final movie in topRatedMovies) {
          if (additionalMovies.length >= needed) break;

          if (movie.id != null &&
              !existingIds.contains(movie.id) &&
              !selectedIds.contains(movie.id)) {
            additionalMovies.add(movie);
            existingIds.add(movie.id!);
          }
        }
      }
    } catch (e) {
      print('❌ خطأ في الحصول على أفلام إضافية: $e');
    }

    return additionalMovies;
  }

  // الحصول على أفلام احتياطية
  Future<List<MovieModel>> _getBackupMovies(
    ApiService apiService,
    List<MovieModel> existingRecommendations,
    int needed,
  ) async {
    final List<MovieModel> backupMovies = [];
    final existingIds = existingRecommendations.map((m) => m.id).toSet();

    try {
      // استخدام الأفلام الشائعة كاحتياطي
      final popularMovies = await apiService.getMovieData(MovieType.popular);

      for (final movie in popularMovies) {
        if (backupMovies.length >= needed) break;

        if (movie.id != null && !existingIds.contains(movie.id)) {
          backupMovies.add(movie);
          existingIds.add(movie.id!);
        }
      }
    } catch (e) {
      print('❌ خطأ في الحصول على أفلام احتياطية: $e');
    }

    return backupMovies;
  }

  // فحص توافق الفيلم مع التفضيلات
  bool _isMovieCompatible(
      MovieModel movie, Map<String, dynamic> userPreferences) {
    // فحص النوعية
    final preferredGenres = userPreferences['preferredGenres'] as List<int>;
    if (preferredGenres.isNotEmpty && movie.genreIds != null) {
      final hasMatchingGenre =
          movie.genreIds!.any((id) => preferredGenres.contains(id));
      if (hasMatchingGenre) return true;
    }

    // فحص التقييم
    final averageRating = userPreferences['averageRating'] as double;
    if (movie.voteAverage != null) {
      final ratingDiff = (movie.voteAverage! - averageRating).abs();
      if (ratingDiff <= 2.0) return true;
    }

    // فحص السنة
    final yearRange = userPreferences['yearRange'] as Map<String, dynamic>;
    if (movie.releaseDate != null && movie.releaseDate!.isNotEmpty) {
      try {
        final movieYear = int.parse(movie.releaseDate!.substring(0, 4));
        final minYear = yearRange['min'] as int;
        final maxYear = yearRange['max'] as int;
        if (movieYear >= minYear - 10 && movieYear <= maxYear + 5) return true;
      } catch (e) {
        // تجاهل أخطاء تحليل التاريخ
      }
    }

    // إذا لم يتطابق مع أي معيار، لا يزال مقبولاً كاحتياطي
    return false;
  }
}
